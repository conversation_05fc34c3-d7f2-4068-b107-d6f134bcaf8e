{"app.theme.switch.dynamic": "Dynamic Theme", "app.theme.switch.auto": "Follow System", "app.theme.switch.light": "Light theme", "app.theme.switch.dark": "Dark theme", "app.theme.switch.compact": "Compact theme", "app.theme.switch.motion.on": "Motion On", "app.theme.switch.motion.off": "Motion Off", "app.theme.switch.happy-work": "Happy Work Effect", "app.header.search": "Search...", "app.header.menu.documentation": "Docs", "app.header.menu.more": "More", "app.header.menu.mobile": "Mobile", "app.header.menu.pro.v4": "Ant Design Pro", "app.header.menu.pro.components": "Ant Design Pro Components", "app.header.menu.charts": "Ant Design Charts", "app.header.menu.ecosystem": "Ecosystem", "app.header.lang": "中文", "app.content.edit-page": "Edit this page on GitHub!", "app.content.edit-demo": "Edit this demo on GitHub!", "app.content.contributors": "contributors", "app.component.examples": "Examples", "app.component.examples.expand": "Expand all code", "app.component.examples.collapse": "Collapse all code", "app.component.examples.visible": "Expand debug examples", "app.component.examples.hide": "Collapse debug examples", "app.component.examples.openDemoNotReact18": "Open Demo with React < 18", "app.component.examples.openDemoWithReact18": "Open Demo with React 18", "app.demo.debug": "Debug only, won't display at online", "app.demo.copy": "Copy code", "app.demo.copied": "Copied!", "app.demo.code.show": "Show code", "app.demo.code.hide": "Hide code", "app.demo.code.hide.simplify": "<PERSON>de", "app.demo.codepen": "Open in CodePen", "app.demo.codesandbox": "Open in CodeSandbox", "app.demo.stackblitz": "Open in Stackblitz", "app.demo.codeblock": "Open in Hitu (This feature is only available in the internal network environment)", "app.demo.separate": "Open in a new window", "app.demo.online": "Online Address", "app.home.introduce": "A design system for enterprise-level products. Create an efficient and enjoyable work experience.", "app.home.pr-welcome": "💡 It is an alpha version and still in progress. Contribution from community is welcome!", "app.home.recommend": "Recommended", "app.home.popularize": "Popular", "app.home.design-and-framework": "Design language and framework", "app.home.design-values": "Design values", "app.home.design-values-description": "This is Ant Design's internal standard for evaluating design quality. Based on the assumption that \"everyone is pursuing happiness at work\", we have added the two values of \"Meaningfulness\" and \"Growth\" on the basis of \"Certainty\" and \"Naturalness\" to guide each designer towards better judgment and decision-making.", "app.home.certainty": "Certainty", "app.home.meaningful": "Meaningfulness", "app.home.growth": "Growth", "app.home.natural": "Naturalness", "app.home.design-guide": "Guidelines", "app.home.components": "Components", "app.home.detail": "More details", "app.home.global-style": "Global style", "app.home.design-patterns": "Design patterns", "app.home.more": "Learn More", "app.home.getting-started": "Getting Started", "app.home.design-language": "Design Language", "app.home.product-antv-slogan": "A new way to do data visualization", "app.home.product-pro-slogan": "Out-of-the-box UI solution for enterprise applications", "app.home.product-mobile-slogan": "Mobile UI components with Ant Design", "app.home.product-hitu": "HiTu", "app.home.product-hitu-slogan": "A new generation of graphical solutions", "app.home.product-kitchen-slogan": "A Sketch plugin to enhance designers", "app.home.product-icons-slogan": "A set of premium icons", "app.home.view-more": "More", "app.footer.repo": "GitHub Repository", "app.footer.awesome": "Awesome Ant Design", "app.footer.course": "Ant Design Practical Tutorial", "app.footer.chinamirror": "China Mirror 🇨🇳", "app.footer.primary-color-changing": "Changing primary color...", "app.footer.primary-color-changed": "Changed primary color successfully!", "app.footer.scaffold": "Scaffold", "app.footer.kitchen": "<PERSON><PERSON><PERSON>", "app.footer.landing": "Landing Templates", "app.footer.scaffolds": "Scaffold Market", "app.footer.dev-tools": "Developer Tools", "app.footer.umi": "React Application Framework", "app.footer.dumi": "Component doc generator", "app.footer.qiankun": "Micro-Frontends Framework", "app.footer.hooks": "React Hooks Library", "app.footer.resources": "Resources", "app.footer.data-vis": "Data Visualization", "app.footer.eggjs": "Enterprise Node Framework", "app.footer.motion": "Motion Solution", "app.footer.community": "Community", "app.footer.help": "Help", "app.footer.change-log": "Change Log", "app.footer.theme": "Theme Editor", "app.footer.faq": "FAQ", "app.footer.feedback": "<PERSON><PERSON><PERSON>", "app.footer.stackoverflow": "StackOverflow", "app.footer.segmentfault": "SegmentFault", "app.footer.discussions": "Discussions", "app.footer.bug-report": "Bug Report", "app.footer.issues": "Issues", "app.footer.version": "Version: ", "app.footer.author": "Created by XTech", "app.footer.work_with_us": "Work with Us", "app.footer.more-product": "More Products", "app.footer.company": "XTech", "app.footer.ant-design": "UI Design Language", "app.footer.yuque": "<PERSON><PERSON><PERSON>", "app.footer.yuque.slogan": "Document Collaboration Platform", "app.footer.antv.slogan": "Data Visualization", "app.footer.egg.slogan": "Enterprise Node.js Framework", "app.footer.yuque.repo": "Ant Design in YuQue", "app.footer.zhihu": "Ant Design in Zhihu", "app.footer.zhihu.xtech": "Experience Cloud Blog", "app.footer.seeconf": "Experience Tech Conference", "app.footer.xtech": "Ant Financial Experience Tech", "app.footer.xtech.slogan": "Experience The Beauty", "app.footer.galacean": "Galacean", "app.footer.galacean.slogan": "Interactive Graphics Solution", "app.footer.weavefox": "WeaveFox", "app.footer.weavefox.slogan": "AI Development with WeaveFox 🦊", "app.docs.color.pick-primary": "Pick your primary color", "app.docs.color.pick-background": "Pick your background color", "app.docs.components.icon.search.placeholder": "Search icons here, click icon to copy code", "app.docs.components.icon.outlined": "Outlined", "app.docs.components.icon.filled": "Filled", "app.docs.components.icon.two-tone": "Two Tone", "app.docs.components.icon.category.direction": "Directional Icons", "app.docs.components.icon.category.suggestion": "Suggested Icons", "app.docs.components.icon.category.editor": "Editor Icons", "app.docs.components.icon.category.data": "Data Icons", "app.docs.components.icon.category.other": "Application Icons", "app.docs.components.icon.category.logo": "Brand and Logos", "app.docs.resource.design": "Design", "app.docs.resource.develop": "Develop", "app.components.overview.search": "Search in components", "app.implementation.community": "community", "app.implementation.official": "official"}