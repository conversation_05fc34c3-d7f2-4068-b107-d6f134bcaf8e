{"app.theme.switch.dynamic": "动态主题", "app.theme.switch.auto": "跟随系统", "app.theme.switch.light": "浅色主题", "app.theme.switch.dark": "暗黑主题", "app.theme.switch.compact": "紧凑主题", "app.theme.switch.motion.on": "动画开启", "app.theme.switch.motion.off": "动画关闭", "app.theme.switch.happy-work": "快乐工作特效", "app.header.search": "全文本搜索...", "app.header.menu.documentation": "文档", "app.header.menu.more": "更多", "app.header.menu.mobile": "移动版", "app.header.menu.pro.v4": "Ant Design Pro", "app.header.menu.pro.components": "Ant Design Pro Components", "app.header.menu.charts": "Ant Design Charts", "app.header.menu.ecosystem": "生态", "app.header.lang": "English", "app.content.edit-page": "在 GitHub 上编辑此页！", "app.content.edit-demo": "在 GitHub 上编辑此示例！", "app.content.contributors": "文档贡献者", "app.component.examples": "代码演示", "app.component.examples.expand": "展开全部代码", "app.component.examples.collapse": "收起全部代码", "app.component.examples.visible": "显示调试专用演示", "app.component.examples.hide": "隐藏调试专用演示", "app.component.examples.openDemoNotReact18": "使用 React 18 以下版本打开 Demo", "app.component.examples.openDemoWithReact18": "使用 React 18 打开 Demo", "app.demo.debug": "此演示仅供调试，线上不会展示", "app.demo.copy": "复制代码", "app.demo.copied": "复制成功", "app.demo.code.show": "显示代码", "app.demo.code.hide": "收起代码", "app.demo.code.hide.simplify": "收起", "app.demo.codepen": "在 CodePen 中打开", "app.demo.codesandbox": "在 CodeSandbox 中打开", "app.demo.stackblitz": "在 Stackblitz 中打开", "app.demo.codeblock": "在海兔中打开（此功能仅在内网环境可用）", "app.demo.separate": "在新窗口打开", "app.demo.online": "线上地址", "app.home.introduce": "企业级产品设计体系，创造高效愉悦的工作体验", "app.home.pr-welcome": "💡 当前为 alpha 版本，仍在开发中。欢迎社区一起共建，让 Ant Design 变得更好！", "app.home.recommend": "精彩推荐", "app.home.popularize": "推广", "app.home.design-and-framework": "设计语言与研发框架", "app.home.design-values": "设计价值观", "app.home.design-values-description": "这是 Ant Design 评价设计好坏的内在标准。基于「每个人都追求快乐工作」这一假定，我们在「确定性」和「自然」的基础上，新增「意义感」和「生长性」两个价值观，指引每个设计者做更好地判断和决策。", "app.home.certainty": "确定性", "app.home.meaningful": "意义感", "app.home.growth": "生长性", "app.home.natural": "自然", "app.home.design-guide": "设计指引", "app.home.components": "组件库", "app.home.detail": "查看详情", "app.home.global-style": "全局样式", "app.home.design-patterns": "设计模式", "app.home.more": "更多内容", "app.home.getting-started": "开始使用", "app.home.design-language": "设计语言", "app.home.product-antv-slogan": "全新一代数据可视化解决方案", "app.home.product-pro-slogan": "开箱即用的中台前端/设计解决方案", "app.home.product-mobile-slogan": "基于 Preact / React / React Native 的 UI 组件库", "app.home.product-hitu": "海兔", "app.home.product-hitu-slogan": "全新一代图形化解决方案", "app.home.product-kitchen-slogan": "一款为设计者提升工作效率的 Sketch 工具集", "app.home.product-icons-slogan": "一整套优质的图标集", "app.home.view-more": "查看全部", "app.footer.repo": "GitHub 仓库", "app.footer.awesome": "Awesome Ant Design", "app.footer.chinamirror": "国内镜像站点 🇨🇳", "app.footer.primary-color-changing": "正在修改主题色...", "app.footer.primary-color-changed": "修改主题色成功！", "app.footer.kitchen": "Sketch 工具集", "app.footer.landing": "首页模板集", "app.footer.scaffold": "脚手架", "app.footer.scaffolds": "脚手架市场", "app.footer.dev-tools": "开发工具", "app.footer.umi": "React 应用开发框架", "app.footer.dumi": "组件/文档研发工具", "app.footer.qiankun": "微前端框架", "app.footer.hooks": "React Hooks 库", "app.footer.resources": "相关资源", "app.footer.data-vis": "数据可视化", "app.footer.eggjs": "企业级 Node 开发框架", "app.footer.motion": "设计动效", "app.footer.community": "社区", "app.footer.help": "帮助", "app.footer.change-log": "更新日志", "app.footer.theme": "主题编辑器", "app.footer.faq": "常见问题", "app.footer.feedback": "反馈和建议", "app.footer.stackoverflow": "StackOverflow", "app.footer.segmentfault": "SegmentFault", "app.footer.discussions": "讨论区", "app.footer.bug-report": "报告 Bug", "app.footer.issues": "议题", "app.footer.version": "文档版本：", "app.footer.author": "蚂蚁集团体验技术部出品 @ XTech", "app.footer.work_with_us": "加入我们", "app.footer.more-product": "更多产品", "app.footer.company": "XTech", "app.footer.ant-design": "蚂蚁 UI 体系", "app.footer.yuque": "语雀", "app.footer.yuque.slogan": "构建你的数字花园", "app.footer.antv.slogan": "数据可视化解决方案", "app.footer.egg.slogan": "企业级 Node.js 框架", "app.footer.yuque.repo": "Ant Design 语雀专栏", "app.footer.zhihu": "Ant Design 知乎专栏", "app.footer.zhihu.xtech": "体验科技专栏", "app.footer.seeconf": "蚂蚁体验科技大会", "app.footer.xtech": "蚂蚁体验科技", "app.footer.xtech.slogan": "让用户体验美好", "app.footer.galacean": "Galacean", "app.footer.galacean.slogan": "互动图形解决方案", "app.footer.weavefox": "WeaveFox", "app.footer.weavefox.slogan": "前端智能研发", "app.docs.color.pick-primary": "选择你的主色", "app.docs.color.pick-background": "选择你的背景色", "app.docs.components.icon.search.placeholder": "在此搜索图标，点击图标可复制代码", "app.docs.components.icon.outlined": "线框风格", "app.docs.components.icon.filled": "实底风格", "app.docs.components.icon.two-tone": "双色风格", "app.docs.components.icon.category.direction": "方向性图标", "app.docs.components.icon.category.suggestion": "提示建议性图标", "app.docs.components.icon.category.editor": "编辑类图标", "app.docs.components.icon.category.data": "数据类图标", "app.docs.components.icon.category.other": "网站通用图标", "app.docs.components.icon.category.logo": "品牌和标识", "app.docs.resource.design": "设计", "app.docs.resource.develop": "开发", "app.components.overview.search": "搜索组件", "app.implementation.community": "社区实现", "app.implementation.official": "官方"}