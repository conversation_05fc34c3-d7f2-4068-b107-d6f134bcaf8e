import * as React from 'react';
import { useState } from 'react';
import { Carousel, Typography } from 'antd';
import { createStyles, css, useTheme } from 'antd-style';

import { getCarouselStyle } from '../util';

const useStyle = createStyles(() => {
  const { carousel } = getCarouselStyle();
  return {
    carousel,
    container: css`
      position: relative;
    `,
    title: css`
      position: absolute;
      top: 15%;
      z-index: 1;
      width: 100%;
      text-align: center;
    `,
    img: css`
      width: 100%;
    `,
  };
});

const mobileImageConfigList = [
  {
    imageSrc:
      'https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*KsMrRZaciFcAAAAAAAAAAAAADrJ8AQ/original',
    titleColor: 'rgba(0,0,0,.88)',
  },
  {
    imageSrc:
      'https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*******************************/original',
    titleColor: '#fff',
  },
  {
    imageSrc:
      'https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*cSX_RbD3k9wAAAAAAAAAAAAADrJ8AQ/original',
    titleColor: '#fff',
  },
  {
    imageSrc:
      'https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*MldsRZeax6EAAAAAAAAAAAAADrJ8AQ/original',
    titleColor: '#fff',
  },
  {
    imageSrc:
      'https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*xCAmSL0xlZ8AAAAAAAAAAAAADrJ8AQ/original',
    titleColor: '#fff',
  },
  {
    imageSrc:
      'https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*vCfCSbiI_VIAAAAAAAAAAAAADrJ8AQ/original',
    titleColor: '#fff',
  },
  {
    imageSrc:
      'https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*xCAmSL0xlZ8AAAAAAAAAAAAADrJ8AQ/original',
    titleColor: '#fff',
  },
  {
    imageSrc:
      'https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*BeDBTY9UnXIAAAAAAAAAAAAADrJ8AQ/original',
    titleColor: '#fff',
  },
  {
    imageSrc:
      'https://mdn.alipayobjects.com/huamei_7uahnr/afts/img/A*Q63XTbk8YaMAAAAAAAAAAAAADrJ8AQ/original',
    titleColor: '#fff',
  },
];

export interface MobileCarouselProps {
  id?: string;
  title?: React.ReactNode;
  description?: React.ReactNode;
}

const MobileCarousel: React.FC<MobileCarouselProps> = (props) => {
  const { styles } = useStyle();
  const { id, title, description } = props;
  const token = useTheme();
  const [currentSlider, setCurrentSlider] = useState<number>(0);

  return (
    <div className={styles.container}>
      <div className={styles.title}>
        <Typography.Title
          id={id}
          level={1}
          style={{
            fontWeight: 900,
            color: mobileImageConfigList[currentSlider].titleColor,
            fontSize: token.fontSizeHeading2,
          }}
        >
          {title}
        </Typography.Title>
        <Typography.Paragraph
          style={{
            marginBottom: token.marginXXL,
            color: mobileImageConfigList[currentSlider].titleColor,
          }}
        >
          {description}
        </Typography.Paragraph>
      </div>
      <Carousel className={styles.carousel} afterChange={setCurrentSlider}>
        {mobileImageConfigList.map((item, index) => (
          <div key={index}>
            <img draggable={false} src={item.imageSrc} className={styles.img} alt="carousel" />
          </div>
        ))}
      </Carousel>
    </div>
  );
};

export default MobileCarousel;
