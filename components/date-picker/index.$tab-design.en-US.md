## Component Definition

The essence of DatePicker is to select (input) date type data.

<code src="./design/behavior-pattern.tsx" inline></code>

## Basic Usage

<code src="./design/demo/pick-date.tsx" description="Used for selecting specific dates. Used when users only need to input very specific date information.">Select a Day</code>

<code src="./design/demo/pick-week.tsx" description="Used for selecting weeks. Used when users only need to input year + week information.">Select a Week</code>

<code src="./design/demo/pick-month.tsx" description="Used for selecting months. Used when users only need to input year + month information.">Select a Month</code>

<code src="./design/demo/pick-quarter.tsx" description="Used for selecting quarters. Used when users only need to input year + quarter information.">Select a Quarter</code>

<code src="./design/demo/pick-year.tsx" description="Used for selecting years. Used when users only need to input year information.">Select a Year</code>

<code src="./design/demo/pick-time.tsx" description="Used for selecting specific moments. Used when users need to input year + month + date + time information.">Select a Moment</code>

<code src="./design/demo/pick-date-range.tsx" description="Used for selecting specific date ranges.">Select from Day to Day</code>

<code src="./design/demo/pick-week-range.tsx" description="Used for selecting week ranges.">Select from Week to Week</code>

<code src="./design/demo/pick-month-range.tsx" description="Used for selecting month ranges.">Select from Month to Month</code>

<code src="./design/demo/pick-quarter-range.tsx" description="Used for selecting quarter ranges.">Select from Quarter to Quarter</code>

<code src="./design/demo/pick-year-range.tsx" description="Used for selecting year ranges.">Select from Year to Year</code>

<code src="./design/demo/pick-time-range.tsx" description="Used for selecting specific moment ranges.">Select from Moment to Moment</code>

## Interactive Variants

<code src="./design/demo/preset-time.tsx" description="Help users quickly complete time point selection through preset options provided in the left area of the panel." tip="According to Hick's Law, it is recommended that the number of shortcut options does not exceed 8.">Quick Select Time Point</code>

<code src="./design/demo/preset-range.tsx" description="Help users quickly complete time range selection through preset options provided in the left area of the panel." tip="According to Hick's Law, it is recommended that the number of shortcut options does not exceed 8.">Quick Select Time Range</code>

<code src="./design/demo/date-extra-info.tsx" description="By defining date cell content and styles, display more business scenario-related information for users as selection reference.">View Date Additional Information</code>
