## Component Definition

The essence of Progress is to understand the progress of a task

<code src="./design/behavior-pattern.tsx" inline></code>

## Basic Usage

<code src="./design/demo/progress" description="Display total progress and completed progress in a linear format, the most basic usage">Understanding Task Progress</code>

<code src="./design/demo/status" description="Understand the current status of the task through the color of the completed progress">Understanding Task Status</code>

## Interactive Variants

<code src="./design/demo/info" description="View progress-related descriptions through text and icons">View Progress-related Descriptions</code>

## Style Variants

<code src="./design/demo/circle" description="Display progress in a circular format, mostly used in scenarios that need to emphasize percentages, such as Dashboard">Circular Progress Bar</code>

<code src="./design/demo/content" description="Micro progress bar suitable for content-level scenarios, often used with text">Content-level Progress Bar</code>
