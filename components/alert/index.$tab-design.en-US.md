## Component Definition

The essence of <PERSON><PERSON> is to understand the alerts that need attention within pages/modules

<code src="./design/behavior-pattern.tsx" inline></code>

## Basic Usage

<code src="./design/demo/content" description="Display alert content, can also be used together with titles">Understanding Alert Content</code>

<code src="./design/demo/type" description="Combined with background colors and icons to understand alert types (success, info, warning, error)">Understanding Alert Types</code>

## Interactive Variants

<code src="./design/demo/action" description="Can perform operations on alerts such as closing, expanding/collapsing, or executing other actions">Perform Operations on Alerts</code>
