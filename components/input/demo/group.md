## zh-CN

`Input.Group` 已废弃，可以使用 [Space.Compact](/components/space-cn#spacecompact) 替代 `Input.Group`。

## en-US

`Input.Group` is deprecated. Can use [Space.Compact](/components/space#spacecompact) substitute for `Input.Group`.

```css
.site-input-group-wrapper .site-input-split {
  background-color: #fff !important;
}

.site-input-group-wrapper .site-input-right {
  border-inline-start-width: 0;
}

.site-input-group-wrapper .site-input-right:hover,
.site-input-group-wrapper .site-input-right:focus {
  border-inline-start-width: 1px;
}

.site-input-group-wrapper .ant-input-rtl.site-input-right {
  border-inline-end-width: 0;
}

.site-input-group-wrapper .ant-input-rtl.site-input-right:hover,
.site-input-group-wrapper .ant-input-rtl.site-input-right:focus {
  border-inline-end-width: 1px;
}
```
