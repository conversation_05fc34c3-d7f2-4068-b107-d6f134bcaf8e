// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/input/demo/addon.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small"
>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-outlined"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <span
          class="ant-input-group-addon"
        >
          http://
        </span>
        <input
          class="ant-input ant-input-outlined"
          type="text"
          value="mysite"
        />
        <span
          class="ant-input-group-addon"
        >
          .com
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-outlined"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <span
          class="ant-input-group-addon"
        >
          <div
            class="ant-select ant-select-outlined ant-select-single ant-select-show-arrow"
          >
            <div
              class="ant-select-selector"
            >
              <span
                class="ant-select-selection-wrap"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="rc_select_TEST_OR_SSR_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="rc_select_TEST_OR_SSR_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="rc_select_TEST_OR_SSR"
                    readonly=""
                    role="combobox"
                    style="opacity: 0;"
                    type="search"
                    unselectable="on"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-item"
                  title="http://"
                >
                  http://
                </span>
              </span>
            </div>
            <div
              class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-select-dropdown-placement-bottomLeft"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
            >
              <div>
                <div
                  id="rc_select_TEST_OR_SSR_list"
                  role="listbox"
                  style="height: 0px; width: 0px; overflow: hidden;"
                >
                  <div
                    aria-label="http://"
                    aria-selected="true"
                    id="rc_select_TEST_OR_SSR_list_0"
                    role="option"
                  >
                    http://
                  </div>
                  <div
                    aria-label="https://"
                    aria-selected="false"
                    id="rc_select_TEST_OR_SSR_list_1"
                    role="option"
                  >
                    https://
                  </div>
                </div>
                <div
                  class="rc-virtual-list"
                  style="position: relative;"
                >
                  <div
                    class="rc-virtual-list-holder"
                    style="max-height: 256px; overflow-y: auto;"
                  >
                    <div>
                      <div
                        class="rc-virtual-list-holder-inner"
                        style="display: flex; flex-direction: column;"
                      >
                        <div
                          aria-selected="true"
                          class="ant-select-item ant-select-item-option ant-select-item-option-active ant-select-item-option-selected"
                          title="http://"
                        >
                          <div
                            class="ant-select-item-option-content"
                          >
                            http://
                          </div>
                          <span
                            aria-hidden="true"
                            class="ant-select-item-option-state"
                            style="user-select: none;"
                            unselectable="on"
                          />
                        </div>
                        <div
                          aria-selected="false"
                          class="ant-select-item ant-select-item-option"
                          title="https://"
                        >
                          <div
                            class="ant-select-item-option-content"
                          >
                            https://
                          </div>
                          <span
                            aria-hidden="true"
                            class="ant-select-item-option-state"
                            style="user-select: none;"
                            unselectable="on"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <span
              aria-hidden="true"
              class="ant-select-arrow"
              style="user-select: none;"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-select-suffix"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
        </span>
        <input
          class="ant-input ant-input-outlined"
          type="text"
          value="mysite"
        />
        <span
          class="ant-input-group-addon"
        >
          <div
            class="ant-select ant-select-outlined ant-select-single ant-select-show-arrow"
          >
            <div
              class="ant-select-selector"
            >
              <span
                class="ant-select-selection-wrap"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="rc_select_TEST_OR_SSR_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="rc_select_TEST_OR_SSR_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="rc_select_TEST_OR_SSR"
                    readonly=""
                    role="combobox"
                    style="opacity: 0;"
                    type="search"
                    unselectable="on"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-item"
                  title=".com"
                >
                  .com
                </span>
              </span>
            </div>
            <div
              class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-select-dropdown-placement-bottomLeft"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
            >
              <div>
                <div
                  id="rc_select_TEST_OR_SSR_list"
                  role="listbox"
                  style="height: 0px; width: 0px; overflow: hidden;"
                >
                  <div
                    aria-label=".com"
                    aria-selected="true"
                    id="rc_select_TEST_OR_SSR_list_0"
                    role="option"
                  >
                    .com
                  </div>
                  <div
                    aria-label=".jp"
                    aria-selected="false"
                    id="rc_select_TEST_OR_SSR_list_1"
                    role="option"
                  >
                    .jp
                  </div>
                </div>
                <div
                  class="rc-virtual-list"
                  style="position: relative;"
                >
                  <div
                    class="rc-virtual-list-holder"
                    style="max-height: 256px; overflow-y: auto;"
                  >
                    <div>
                      <div
                        class="rc-virtual-list-holder-inner"
                        style="display: flex; flex-direction: column;"
                      >
                        <div
                          aria-selected="true"
                          class="ant-select-item ant-select-item-option ant-select-item-option-active ant-select-item-option-selected"
                          title=".com"
                        >
                          <div
                            class="ant-select-item-option-content"
                          >
                            .com
                          </div>
                          <span
                            aria-hidden="true"
                            class="ant-select-item-option-state"
                            style="user-select: none;"
                            unselectable="on"
                          />
                        </div>
                        <div
                          aria-selected="false"
                          class="ant-select-item ant-select-item-option"
                          title=".jp"
                        >
                          <div
                            class="ant-select-item-option-content"
                          >
                            .jp
                          </div>
                          <span
                            aria-hidden="true"
                            class="ant-select-item-option-state"
                            style="user-select: none;"
                            unselectable="on"
                          />
                        </div>
                        <div
                          aria-selected="false"
                          class="ant-select-item ant-select-item-option"
                          title=".cn"
                        >
                          <div
                            class="ant-select-item-option-content"
                          >
                            .cn
                          </div>
                          <span
                            aria-hidden="true"
                            class="ant-select-item-option-state"
                            style="user-select: none;"
                            unselectable="on"
                          />
                        </div>
                        <div
                          aria-selected="false"
                          class="ant-select-item ant-select-item-option"
                          title=".org"
                        >
                          <div
                            class="ant-select-item-option-content"
                          >
                            .org
                          </div>
                          <span
                            aria-hidden="true"
                            class="ant-select-item-option-state"
                            style="user-select: none;"
                            unselectable="on"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <span
              aria-hidden="true"
              class="ant-select-arrow"
              style="user-select: none;"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-select-suffix"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-outlined"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <input
          class="ant-input ant-input-outlined"
          type="text"
          value="mysite"
        />
        <span
          class="ant-input-group-addon"
        >
          <span
            aria-label="setting"
            class="anticon anticon-setting"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="setting"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
              />
            </svg>
          </span>
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-outlined"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <span
          class="ant-input-group-addon"
        >
          http://
        </span>
        <span
          class="ant-input-affix-wrapper ant-input-outlined"
        >
          <input
            class="ant-input"
            type="text"
            value="mysite"
          />
          <span
            class="ant-input-suffix"
          >
            .com
          </span>
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-outlined"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <span
          class="ant-input-group-addon"
        >
          <div
            class="ant-select ant-cascader ant-select-outlined ant-select-single ant-select-allow-clear ant-select-show-arrow"
            style="width: 150px;"
          >
            <div
              class="ant-select-selector"
            >
              <span
                class="ant-select-selection-wrap"
              >
                <span
                  class="ant-select-selection-search"
                >
                  <input
                    aria-autocomplete="list"
                    aria-controls="rc_select_TEST_OR_SSR_list"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    aria-owns="rc_select_TEST_OR_SSR_list"
                    autocomplete="off"
                    class="ant-select-selection-search-input"
                    id="rc_select_TEST_OR_SSR"
                    readonly=""
                    role="combobox"
                    style="opacity: 0;"
                    type="search"
                    unselectable="on"
                    value=""
                  />
                </span>
                <span
                  class="ant-select-selection-placeholder"
                >
                  cascader
                </span>
              </span>
            </div>
            <div
              class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-cascader-dropdown ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
              style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
            >
              <div>
                <div
                  class="ant-cascader-menus ant-cascader-menu-empty"
                >
                  <ul
                    class="ant-cascader-menu"
                    role="menu"
                  >
                    <li
                      aria-checked="false"
                      class="ant-cascader-menu-item ant-cascader-menu-item-disabled"
                      data-path-key="__EMPTY__"
                      role="menuitemcheckbox"
                    >
                      <div
                        class="ant-cascader-menu-item-content"
                      >
                        <div
                          class="ant-empty ant-empty-normal ant-empty-small"
                        >
                          <div
                            class="ant-empty-image"
                          >
                            <svg
                              height="41"
                              viewBox="0 0 64 41"
                              width="64"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <title>
                                No data
                              </title>
                              <g
                                fill="none"
                                fill-rule="evenodd"
                                transform="translate(0 1)"
                              >
                                <ellipse
                                  cx="32"
                                  cy="33"
                                  fill="#f5f5f5"
                                  rx="32"
                                  ry="7"
                                />
                                <g
                                  fill-rule="nonzero"
                                  stroke="#d9d9d9"
                                >
                                  <path
                                    d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                                  />
                                  <path
                                    d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                                    fill="#fafafa"
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                          <div
                            class="ant-empty-description"
                          >
                            No data
                          </div>
                        </div>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            <span
              aria-hidden="true"
              class="ant-select-arrow"
              style="user-select: none;"
              unselectable="on"
            >
              <span
                aria-label="down"
                class="anticon anticon-down ant-select-suffix"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="down"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                  />
                </svg>
              </span>
            </span>
          </div>
        </span>
        <input
          class="ant-input ant-input-outlined"
          type="text"
          value="mysite"
        />
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/input/demo/addon.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/advance-count.tsx extend context correctly 1`] = `
<div
  class="ant-flex ant-flex-align-stretch ant-flex-vertical"
  style="gap: 16px;"
>
  <div>
    <h5
      class="ant-typography"
    >
      Exceed Max
    </h5>
    <span
      class="ant-input-affix-wrapper ant-input-outlined ant-input-out-of-range"
    >
      <input
        class="ant-input"
        type="text"
        value="Hello, antd!"
      />
      <span
        class="ant-input-suffix"
      >
        <span
          class="ant-input-show-count-suffix"
        >
          12 / 10
        </span>
      </span>
    </span>
  </div>
  <div>
    <h5
      class="ant-typography"
    >
      Emoji count as length 1
    </h5>
    <span
      class="ant-input-affix-wrapper ant-input-outlined"
    >
      <input
        class="ant-input"
        type="text"
        value="🔥🔥🔥"
      />
      <span
        class="ant-input-suffix"
      >
        <span
          class="ant-input-show-count-suffix"
        >
          3
        </span>
      </span>
    </span>
  </div>
  <div>
    <h5
      class="ant-typography"
    >
      Not exceed max
    </h5>
    <span
      class="ant-input-affix-wrapper ant-input-outlined"
    >
      <input
        class="ant-input"
        type="text"
        value="🔥 antd"
      />
      <span
        class="ant-input-suffix"
      >
        <span
          class="ant-input-show-count-suffix"
        >
          6 / 6
        </span>
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/input/demo/advance-count.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/align.tsx extend context correctly 1`] = `
Array [
  <div
    class="ant-mentions ant-mentions-outlined"
    style="width: 100px;"
  >
    <textarea
      class="rc-textarea"
      rows="1"
    />
  </div>,
  <textarea
    class="ant-input ant-input-outlined"
    rows="1"
    style="width: 100px;"
  />,
  <button
    class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      Button
    </span>
  </button>,
  <input
    class="ant-input ant-input-outlined"
    style="width: 100px;"
    type="text"
    value=""
  />,
  <span
    class="ant-typography"
  >
    Ant Design
    <button
      aria-describedby="test-id"
      aria-label="Copy"
      class="ant-typography-copy"
      type="button"
    >
      <span
        aria-label="copy"
        class="anticon anticon-copy"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="copy"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M832 64H296c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h496v688c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8V96c0-17.7-14.3-32-32-32zM704 192H192c-17.7 0-32 14.3-32 32v530.7c0 8.5 3.4 16.6 9.4 22.6l173.3 173.3c2.2 2.2 4.7 4 7.4 5.5v1.9h4.2c3.5 1.3 7.2 2 11 2H704c17.7 0 32-14.3 32-32V224c0-17.7-14.3-32-32-32zM350 856.2L263.9 770H350v86.2zM664 888H414V746c0-22.1-17.9-40-40-40H232V264h432v624z"
          />
        </svg>
      </span>
    </button>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast ant-tooltip-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        >
          Copy
        </div>
      </div>
    </div>
  </span>,
  <span
    class="ant-input-affix-wrapper ant-input-outlined"
    style="width: 100px;"
  >
    <span
      class="ant-input-prefix"
    >
      1
    </span>
    <input
      class="ant-input"
      type="text"
      value=""
    />
    <span
      class="ant-input-suffix"
    >
      2
    </span>
  </span>,
  <span
    class="ant-input-group-wrapper ant-input-group-wrapper-outlined"
    style="width: 100px;"
  >
    <span
      class="ant-input-wrapper ant-input-group"
    >
      <span
        class="ant-input-group-addon"
      >
        1
      </span>
      <input
        class="ant-input ant-input-outlined"
        type="text"
        value=""
      />
      <span
        class="ant-input-group-addon"
      >
        2
      </span>
    </span>
  </span>,
  <div
    class="ant-input-number ant-input-number-outlined"
    style="width: 100px;"
  >
    <div
      class="ant-input-number-handler-wrap"
    >
      <span
        aria-disabled="false"
        aria-label="Increase Value"
        class="ant-input-number-handler ant-input-number-handler-up"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="up"
          class="anticon anticon-up ant-input-number-handler-up-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="up"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
            />
          </svg>
        </span>
      </span>
      <span
        aria-disabled="false"
        aria-label="Decrease Value"
        class="ant-input-number-handler ant-input-number-handler-down"
        role="button"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-input-number-handler-down-inner"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-input-number-input-wrap"
    >
      <input
        autocomplete="off"
        class="ant-input-number-input"
        role="spinbutton"
        step="1"
        value=""
      />
    </div>
  </div>,
  <div
    class="ant-picker ant-picker-outlined"
    style="width: 100px;"
  >
    <div
      class="ant-picker-input"
    >
      <input
        aria-invalid="false"
        autocomplete="off"
        placeholder="Select date"
        size="12"
        value=""
      />
      <span
        class="ant-picker-suffix"
      >
        <span
          aria-label="calendar"
          class="anticon anticon-calendar"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="calendar"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
            />
          </svg>
        </span>
      </span>
    </div>
  </div>,
  <div
    class="ant-picker-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-picker-dropdown-placement-bottomLeft"
    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
  >
    <div
      class="ant-picker-panel-container ant-picker-date-panel-container"
      style="margin-left: 0px; margin-right: auto;"
      tabindex="-1"
    >
      <div
        class="ant-picker-panel-layout"
      >
        <div>
          <div
            class="ant-picker-panel"
            tabindex="0"
          >
            <div
              class="ant-picker-date-panel"
            >
              <div
                class="ant-picker-header"
              >
                <button
                  aria-label="Last year (Control + left)"
                  class="ant-picker-header-super-prev-btn"
                  tabindex="-1"
                  type="button"
                >
                  <span
                    class="ant-picker-super-prev-icon"
                  />
                </button>
                <button
                  aria-label="Previous month (PageUp)"
                  class="ant-picker-header-prev-btn"
                  tabindex="-1"
                  type="button"
                >
                  <span
                    class="ant-picker-prev-icon"
                  />
                </button>
                <div
                  class="ant-picker-header-view"
                >
                  <button
                    aria-label="Choose a month"
                    class="ant-picker-month-btn"
                    tabindex="-1"
                    type="button"
                  >
                    Nov
                  </button>
                  <button
                    aria-label="Choose a year"
                    class="ant-picker-year-btn"
                    tabindex="-1"
                    type="button"
                  >
                    2016
                  </button>
                </div>
                <button
                  aria-label="Next month (PageDown)"
                  class="ant-picker-header-next-btn"
                  tabindex="-1"
                  type="button"
                >
                  <span
                    class="ant-picker-next-icon"
                  />
                </button>
                <button
                  aria-label="Next year (Control + right)"
                  class="ant-picker-header-super-next-btn"
                  tabindex="-1"
                  type="button"
                >
                  <span
                    class="ant-picker-super-next-icon"
                  />
                </button>
              </div>
              <div
                class="ant-picker-body"
              >
                <table
                  class="ant-picker-content"
                >
                  <thead>
                    <tr>
                      <th>
                        Su
                      </th>
                      <th>
                        Mo
                      </th>
                      <th>
                        Tu
                      </th>
                      <th>
                        We
                      </th>
                      <th>
                        Th
                      </th>
                      <th>
                        Fr
                      </th>
                      <th>
                        Sa
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td
                        class="ant-picker-cell"
                        title="2016-10-30"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          30
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell"
                        title="2016-10-31"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          31
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-01"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          1
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-02"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          2
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-03"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          3
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-04"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          4
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-05"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          5
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-06"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          6
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-07"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          7
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-08"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          8
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-09"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          9
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-10"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          10
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-11"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          11
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-12"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          12
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-13"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          13
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-14"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          14
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-15"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          15
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-16"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          16
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-17"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          17
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-18"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          18
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-19"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          19
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-20"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          20
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-21"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          21
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view ant-picker-cell-today"
                        title="2016-11-22"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          22
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-23"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          23
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-24"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          24
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-25"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          25
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-26"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          26
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-27"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          27
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-28"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          28
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-29"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          29
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11-30"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          30
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell"
                        title="2016-12-01"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          1
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell"
                        title="2016-12-02"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          2
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell"
                        title="2016-12-03"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          3
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td
                        class="ant-picker-cell"
                        title="2016-12-04"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          4
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell"
                        title="2016-12-05"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          5
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell"
                        title="2016-12-06"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          6
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell"
                        title="2016-12-07"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          7
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell"
                        title="2016-12-08"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          8
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell"
                        title="2016-12-09"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          9
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell"
                        title="2016-12-10"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          10
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div
            class="ant-picker-footer"
          >
            <ul
              class="ant-picker-ranges"
            >
              <li
                class="ant-picker-now"
              >
                <a
                  aria-disabled="false"
                  class="ant-picker-now-btn"
                >
                  Today
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-picker ant-picker-outlined"
    style="width: 100px;"
  >
    <div
      class="ant-picker-input"
    >
      <input
        aria-invalid="false"
        autocomplete="off"
        placeholder="Select time"
        size="10"
        value=""
      />
      <span
        class="ant-picker-suffix"
      >
        <span
          aria-label="clock-circle"
          class="anticon anticon-clock-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="clock-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
            />
          </svg>
        </span>
      </span>
    </div>
  </div>,
  <div
    class="ant-picker-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-picker-dropdown-placement-bottomLeft"
    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
  >
    <div
      class="ant-picker-panel-container ant-picker-time-panel-container"
      style="margin-left: 0px; margin-right: auto;"
      tabindex="-1"
    >
      <div
        class="ant-picker-panel-layout"
      >
        <div>
          <div
            class="ant-picker-panel"
            tabindex="0"
          >
            <div
              class="ant-picker-time-panel"
            >
              <div
                class="ant-picker-content"
              >
                <ul
                  class="ant-picker-time-panel-column"
                  data-type="hour"
                >
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="0"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      00
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="1"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      01
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="2"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      02
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="3"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      03
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="4"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      04
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="5"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      05
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="6"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      06
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="7"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      07
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="8"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      08
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="9"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      09
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="10"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      10
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="11"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      11
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="12"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      12
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="13"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      13
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="14"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      14
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="15"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      15
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="16"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      16
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="17"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      17
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="18"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      18
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="19"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      19
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="20"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      20
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="21"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      21
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="22"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      22
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="23"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      23
                    </div>
                  </li>
                </ul>
                <ul
                  class="ant-picker-time-panel-column"
                  data-type="minute"
                >
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="0"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      00
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="1"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      01
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="2"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      02
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="3"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      03
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="4"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      04
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="5"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      05
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="6"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      06
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="7"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      07
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="8"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      08
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="9"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      09
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="10"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      10
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="11"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      11
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="12"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      12
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="13"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      13
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="14"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      14
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="15"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      15
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="16"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      16
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="17"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      17
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="18"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      18
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="19"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      19
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="20"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      20
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="21"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      21
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="22"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      22
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="23"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      23
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="24"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      24
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="25"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      25
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="26"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      26
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="27"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      27
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="28"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      28
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="29"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      29
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="30"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      30
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="31"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      31
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="32"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      32
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="33"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      33
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="34"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      34
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="35"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      35
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="36"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      36
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="37"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      37
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="38"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      38
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="39"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      39
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="40"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      40
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="41"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      41
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="42"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      42
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="43"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      43
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="44"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      44
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="45"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      45
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="46"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      46
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="47"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      47
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="48"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      48
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="49"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      49
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="50"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      50
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="51"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      51
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="52"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      52
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="53"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      53
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="54"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      54
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="55"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      55
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="56"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      56
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="57"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      57
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="58"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      58
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="59"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      59
                    </div>
                  </li>
                </ul>
                <ul
                  class="ant-picker-time-panel-column"
                  data-type="second"
                >
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="0"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      00
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="1"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      01
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="2"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      02
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="3"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      03
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="4"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      04
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="5"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      05
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="6"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      06
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="7"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      07
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="8"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      08
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="9"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      09
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="10"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      10
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="11"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      11
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="12"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      12
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="13"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      13
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="14"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      14
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="15"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      15
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="16"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      16
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="17"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      17
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="18"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      18
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="19"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      19
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="20"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      20
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="21"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      21
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="22"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      22
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="23"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      23
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="24"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      24
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="25"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      25
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="26"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      26
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="27"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      27
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="28"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      28
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="29"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      29
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="30"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      30
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="31"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      31
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="32"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      32
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="33"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      33
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="34"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      34
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="35"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      35
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="36"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      36
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="37"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      37
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="38"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      38
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="39"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      39
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="40"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      40
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="41"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      41
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="42"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      42
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="43"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      43
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="44"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      44
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="45"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      45
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="46"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      46
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="47"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      47
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="48"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      48
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="49"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      49
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="50"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      50
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="51"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      51
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="52"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      52
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="53"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      53
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="54"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      54
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="55"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      55
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="56"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      56
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="57"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      57
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="58"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      58
                    </div>
                  </li>
                  <li
                    class="ant-picker-time-panel-cell"
                    data-value="59"
                  >
                    <div
                      class="ant-picker-time-panel-cell-inner"
                    >
                      59
                    </div>
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div
            class="ant-picker-footer"
          >
            <ul
              class="ant-picker-ranges"
            >
              <li
                class="ant-picker-now"
              >
                <a
                  aria-disabled="false"
                  class="ant-picker-now-btn"
                >
                  Now
                </a>
              </li>
              <li
                class="ant-picker-ok"
              >
                <button
                  class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-sm"
                  disabled=""
                  type="button"
                >
                  <span>
                    OK
                  </span>
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-select ant-select-outlined ant-select-single ant-select-show-arrow"
    style="width: 100px;"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="rc_select_TEST_OR_SSR_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="rc_select_TEST_OR_SSR_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="rc_select_TEST_OR_SSR"
            readonly=""
            role="combobox"
            style="opacity: 0;"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-item"
          title="Jack"
        >
          Jack
        </span>
      </span>
    </div>
    <div
      class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-select-dropdown-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div>
        <div
          id="rc_select_TEST_OR_SSR_list"
          role="listbox"
          style="height: 0px; width: 0px; overflow: hidden;"
        >
          <div
            aria-label="Jack"
            aria-selected="true"
            id="rc_select_TEST_OR_SSR_list_0"
            role="option"
          >
            jack
          </div>
          <div
            aria-label="Lucy"
            aria-selected="false"
            id="rc_select_TEST_OR_SSR_list_1"
            role="option"
          >
            lucy
          </div>
        </div>
        <div
          class="rc-virtual-list"
          style="position: relative;"
        >
          <div
            class="rc-virtual-list-holder"
            style="max-height: 256px; overflow-y: auto;"
          >
            <div>
              <div
                class="rc-virtual-list-holder-inner"
                style="display: flex; flex-direction: column;"
              >
                <div
                  aria-selected="true"
                  class="ant-select-item ant-select-item-option ant-select-item-option-active ant-select-item-option-selected"
                  title="Jack"
                >
                  <div
                    class="ant-select-item-option-content"
                  >
                    Jack
                  </div>
                  <span
                    aria-hidden="true"
                    class="ant-select-item-option-state"
                    style="user-select: none;"
                    unselectable="on"
                  />
                </div>
                <div
                  aria-selected="false"
                  class="ant-select-item ant-select-item-option"
                  title="Lucy"
                >
                  <div
                    class="ant-select-item-option-content"
                  >
                    Lucy
                  </div>
                  <span
                    aria-hidden="true"
                    class="ant-select-item-option-state"
                    style="user-select: none;"
                    unselectable="on"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select: none;"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>,
  <div
    class="ant-select ant-select-outlined ant-select-single ant-select-show-arrow"
    style="width: 100px;"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="rc_select_TEST_OR_SSR_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="rc_select_TEST_OR_SSR_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="rc_select_TEST_OR_SSR"
            readonly=""
            role="combobox"
            style="opacity: 0;"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-item"
          title=""
        />
      </span>
    </div>
    <div
      class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-select-dropdown-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div>
        <div
          id="rc_select_TEST_OR_SSR_list"
          role="listbox"
          style="height: 0px; width: 0px; overflow: hidden;"
        >
          <div
            aria-label="Jack"
            aria-selected="false"
            id="rc_select_TEST_OR_SSR_list_0"
            role="option"
          >
            jack
          </div>
          <div
            aria-label="Lucy"
            aria-selected="false"
            id="rc_select_TEST_OR_SSR_list_1"
            role="option"
          >
            lucy
          </div>
        </div>
        <div
          class="rc-virtual-list"
          style="position: relative;"
        >
          <div
            class="rc-virtual-list-holder"
            style="max-height: 256px; overflow-y: auto;"
          >
            <div>
              <div
                class="rc-virtual-list-holder-inner"
                style="display: flex; flex-direction: column;"
              >
                <div
                  aria-selected="false"
                  class="ant-select-item ant-select-item-option ant-select-item-option-active"
                  title="Jack"
                >
                  <div
                    class="ant-select-item-option-content"
                  >
                    Jack
                  </div>
                  <span
                    aria-hidden="true"
                    class="ant-select-item-option-state"
                    style="user-select: none;"
                    unselectable="on"
                  />
                </div>
                <div
                  aria-selected="false"
                  class="ant-select-item ant-select-item-option"
                  title="Lucy"
                >
                  <div
                    class="ant-select-item-option-content"
                  >
                    Lucy
                  </div>
                  <span
                    aria-hidden="true"
                    class="ant-select-item-option-state"
                    style="user-select: none;"
                    unselectable="on"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select: none;"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>,
  <div
    class="ant-select ant-select-outlined ant-select-single ant-select-show-arrow"
    style="width: 100px;"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="rc_select_TEST_OR_SSR_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="rc_select_TEST_OR_SSR_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="rc_select_TEST_OR_SSR"
            readonly=""
            role="combobox"
            style="opacity: 0;"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        />
      </span>
    </div>
    <div
      class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-select-dropdown-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div>
        <div
          id="rc_select_TEST_OR_SSR_list"
          role="listbox"
          style="height: 0px; width: 0px; overflow: hidden;"
        >
          <div
            aria-label="Jack"
            aria-selected="false"
            id="rc_select_TEST_OR_SSR_list_0"
            role="option"
          >
            jack
          </div>
          <div
            aria-label="Lucy"
            aria-selected="false"
            id="rc_select_TEST_OR_SSR_list_1"
            role="option"
          >
            lucy
          </div>
        </div>
        <div
          class="rc-virtual-list"
          style="position: relative;"
        >
          <div
            class="rc-virtual-list-holder"
            style="max-height: 256px; overflow-y: auto;"
          >
            <div>
              <div
                class="rc-virtual-list-holder-inner"
                style="display: flex; flex-direction: column;"
              >
                <div
                  aria-selected="false"
                  class="ant-select-item ant-select-item-option ant-select-item-option-active"
                  title="Jack"
                >
                  <div
                    class="ant-select-item-option-content"
                  >
                    Jack
                  </div>
                  <span
                    aria-hidden="true"
                    class="ant-select-item-option-state"
                    style="user-select: none;"
                    unselectable="on"
                  />
                </div>
                <div
                  aria-selected="false"
                  class="ant-select-item ant-select-item-option"
                  title="Lucy"
                >
                  <div
                    class="ant-select-item-option-content"
                  >
                    Lucy
                  </div>
                  <span
                    aria-hidden="true"
                    class="ant-select-item-option-state"
                    style="user-select: none;"
                    unselectable="on"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select: none;"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>,
  <div
    class="ant-select ant-tree-select ant-select-outlined ant-select-single ant-select-show-arrow"
    style="width: 100px;"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="rc_select_TEST_OR_SSR_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="rc_select_TEST_OR_SSR_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="rc_select_TEST_OR_SSR"
            readonly=""
            role="combobox"
            style="opacity: 0;"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        />
      </span>
    </div>
    <div
      class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-tree-select-dropdown ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div>
        <div
          class="ant-select-empty"
          role="listbox"
        >
          <div
            class="ant-empty ant-empty-normal ant-empty-small"
          >
            <div
              class="ant-empty-image"
            >
              <svg
                height="41"
                viewBox="0 0 64 41"
                width="64"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>
                  No data
                </title>
                <g
                  fill="none"
                  fill-rule="evenodd"
                  transform="translate(0 1)"
                >
                  <ellipse
                    cx="32"
                    cy="33"
                    fill="#f5f5f5"
                    rx="32"
                    ry="7"
                  />
                  <g
                    fill-rule="nonzero"
                    stroke="#d9d9d9"
                  >
                    <path
                      d="M55 12.76L44.854 1.258C44.367.474 43.656 0 42.907 0H21.093c-.749 0-1.46.474-1.947 1.257L9 12.761V22h46v-9.24z"
                    />
                    <path
                      d="M41.613 15.931c0-1.605.994-2.93 2.227-2.931H55v18.137C55 33.26 53.68 35 52.05 35h-40.1C10.32 35 9 33.259 9 31.137V13h11.16c1.233 0 2.227 1.323 2.227 2.928v.022c0 1.605 1.005 2.901 2.237 2.901h14.752c1.232 0 2.237-1.308 2.237-2.913v-.007z"
                      fill="#fafafa"
                    />
                  </g>
                </g>
              </svg>
            </div>
            <div
              class="ant-empty-description"
            >
              No data
            </div>
          </div>
        </div>
      </div>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select: none;"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
  </div>,
  <div
    class="ant-select ant-cascader ant-select-outlined ant-select-single ant-select-allow-clear ant-select-show-arrow"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="rc_select_TEST_OR_SSR_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="rc_select_TEST_OR_SSR_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="rc_select_TEST_OR_SSR"
            readonly=""
            role="combobox"
            style="opacity: 0;"
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-item"
          title="Zhejiang / Hangzhou / West Lake"
        >
          Zhejiang / Hangzhou / West Lake
        </span>
      </span>
    </div>
    <div
      class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-cascader-dropdown ant-select-dropdown-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box; min-width: auto;"
    >
      <div>
        <div
          class="ant-cascader-menus"
        >
          <ul
            class="ant-cascader-menu"
            role="menu"
          >
            <li
              aria-checked="false"
              class="ant-cascader-menu-item ant-cascader-menu-item-expand"
              data-path-key="zhejiang"
              role="menuitemcheckbox"
              title="Zhejiang"
            >
              <div
                class="ant-cascader-menu-item-content"
              >
                Zhejiang
              </div>
              <div
                class="ant-cascader-menu-item-expand-icon"
              >
                <span
                  aria-label="right"
                  class="anticon anticon-right"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="right"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    />
                  </svg>
                </span>
              </div>
            </li>
            <li
              aria-checked="false"
              class="ant-cascader-menu-item ant-cascader-menu-item-expand"
              data-path-key="jiangsu"
              role="menuitemcheckbox"
              title="Jiangsu"
            >
              <div
                class="ant-cascader-menu-item-content"
              >
                Jiangsu
              </div>
              <div
                class="ant-cascader-menu-item-expand-icon"
              >
                <span
                  aria-label="right"
                  class="anticon anticon-right"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="right"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    />
                  </svg>
                </span>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <span
      aria-hidden="true"
      class="ant-select-arrow"
      style="user-select: none;"
      unselectable="on"
    >
      <span
        aria-label="down"
        class="anticon anticon-down ant-select-suffix"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="down"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
          />
        </svg>
      </span>
    </span>
    <span
      aria-hidden="true"
      class="ant-select-clear"
      style="user-select: none;"
      unselectable="on"
    >
      <span
        aria-label="close-circle"
        class="anticon anticon-close-circle"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="close-circle"
          fill="currentColor"
          fill-rule="evenodd"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
          />
        </svg>
      </span>
    </span>
  </div>,
  <div
    class="ant-picker ant-picker-range ant-picker-outlined"
  >
    <div
      class="ant-picker-input"
    >
      <input
        aria-invalid="false"
        autocomplete="off"
        date-range="start"
        placeholder="Start date"
        size="12"
        value=""
      />
    </div>
    <div
      class="ant-picker-range-separator"
    >
      <span
        aria-label="to"
        class="ant-picker-separator"
      >
        <span
          aria-label="swap-right"
          class="anticon anticon-swap-right"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="swap-right"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="0 0 1024 1024"
            width="1em"
          >
            <path
              d="M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-picker-input"
    >
      <input
        aria-invalid="false"
        autocomplete="off"
        date-range="end"
        placeholder="End date"
        size="12"
        value=""
      />
    </div>
    <div
      class="ant-picker-active-bar"
      style="position: absolute; width: 0px;"
    />
    <span
      class="ant-picker-suffix"
    >
      <span
        aria-label="calendar"
        class="anticon anticon-calendar"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="calendar"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
          />
        </svg>
      </span>
    </span>
  </div>,
  <div
    class="ant-picker-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-picker-dropdown-range ant-picker-dropdown-placement-bottomLeft"
    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
  >
    <div
      class="ant-picker-range-wrapper ant-picker-date-range-wrapper"
    >
      <div
        class="ant-picker-range-arrow"
        style="left: 0px;"
      />
      <div
        class="ant-picker-panel-container ant-picker-date-panel-container"
        style="margin-left: 0px; margin-right: auto;"
        tabindex="-1"
      >
        <div
          class="ant-picker-panel-layout"
        >
          <div>
            <div
              class="ant-picker-panels"
            >
              <div
                class="ant-picker-panel"
                tabindex="0"
              >
                <div
                  class="ant-picker-date-panel"
                >
                  <div
                    class="ant-picker-header"
                  >
                    <button
                      aria-label="Last year (Control + left)"
                      class="ant-picker-header-super-prev-btn"
                      tabindex="-1"
                      type="button"
                    >
                      <span
                        class="ant-picker-super-prev-icon"
                      />
                    </button>
                    <button
                      aria-label="Previous month (PageUp)"
                      class="ant-picker-header-prev-btn"
                      tabindex="-1"
                      type="button"
                    >
                      <span
                        class="ant-picker-prev-icon"
                      />
                    </button>
                    <div
                      class="ant-picker-header-view"
                    >
                      <button
                        aria-label="Choose a month"
                        class="ant-picker-month-btn"
                        tabindex="-1"
                        type="button"
                      >
                        Nov
                      </button>
                      <button
                        aria-label="Choose a year"
                        class="ant-picker-year-btn"
                        tabindex="-1"
                        type="button"
                      >
                        2016
                      </button>
                    </div>
                    <button
                      aria-label="Next month (PageDown)"
                      class="ant-picker-header-next-btn"
                      style="visibility: hidden;"
                      tabindex="-1"
                      type="button"
                    >
                      <span
                        class="ant-picker-next-icon"
                      />
                    </button>
                    <button
                      aria-label="Next year (Control + right)"
                      class="ant-picker-header-super-next-btn"
                      style="visibility: hidden;"
                      tabindex="-1"
                      type="button"
                    >
                      <span
                        class="ant-picker-super-next-icon"
                      />
                    </button>
                  </div>
                  <div
                    class="ant-picker-body"
                  >
                    <table
                      class="ant-picker-content"
                    >
                      <thead>
                        <tr>
                          <th>
                            Su
                          </th>
                          <th>
                            Mo
                          </th>
                          <th>
                            Tu
                          </th>
                          <th>
                            We
                          </th>
                          <th>
                            Th
                          </th>
                          <th>
                            Fr
                          </th>
                          <th>
                            Sa
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td
                            class="ant-picker-cell"
                            title="2016-10-30"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              30
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2016-10-31"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              31
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-01"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              1
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-02"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              2
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-03"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              3
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-04"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              4
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-05"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              5
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-06"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              6
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-07"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              7
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-08"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              8
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-09"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              9
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-10"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              10
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-11"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              11
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-12"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              12
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-13"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              13
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-14"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              14
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-15"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              15
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-16"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              16
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-17"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              17
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-18"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              18
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-19"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              19
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-20"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              20
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-21"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              21
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view ant-picker-cell-today"
                            title="2016-11-22"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              22
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-23"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              23
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-24"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              24
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-25"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              25
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-26"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              26
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-27"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              27
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-28"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              28
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-29"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              29
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-11-30"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              30
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2016-12-01"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              1
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2016-12-02"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              2
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2016-12-03"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              3
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td
                            class="ant-picker-cell"
                            title="2016-12-04"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              4
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2016-12-05"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              5
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2016-12-06"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              6
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2016-12-07"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              7
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2016-12-08"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              8
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2016-12-09"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              9
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2016-12-10"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              10
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
              <div
                class="ant-picker-panel"
                tabindex="0"
              >
                <div
                  class="ant-picker-date-panel"
                >
                  <div
                    class="ant-picker-header"
                  >
                    <button
                      aria-label="Last year (Control + left)"
                      class="ant-picker-header-super-prev-btn"
                      style="visibility: hidden;"
                      tabindex="-1"
                      type="button"
                    >
                      <span
                        class="ant-picker-super-prev-icon"
                      />
                    </button>
                    <button
                      aria-label="Previous month (PageUp)"
                      class="ant-picker-header-prev-btn"
                      style="visibility: hidden;"
                      tabindex="-1"
                      type="button"
                    >
                      <span
                        class="ant-picker-prev-icon"
                      />
                    </button>
                    <div
                      class="ant-picker-header-view"
                    >
                      <button
                        aria-label="Choose a month"
                        class="ant-picker-month-btn"
                        tabindex="-1"
                        type="button"
                      >
                        Dec
                      </button>
                      <button
                        aria-label="Choose a year"
                        class="ant-picker-year-btn"
                        tabindex="-1"
                        type="button"
                      >
                        2016
                      </button>
                    </div>
                    <button
                      aria-label="Next month (PageDown)"
                      class="ant-picker-header-next-btn"
                      tabindex="-1"
                      type="button"
                    >
                      <span
                        class="ant-picker-next-icon"
                      />
                    </button>
                    <button
                      aria-label="Next year (Control + right)"
                      class="ant-picker-header-super-next-btn"
                      tabindex="-1"
                      type="button"
                    >
                      <span
                        class="ant-picker-super-next-icon"
                      />
                    </button>
                  </div>
                  <div
                    class="ant-picker-body"
                  >
                    <table
                      class="ant-picker-content"
                    >
                      <thead>
                        <tr>
                          <th>
                            Su
                          </th>
                          <th>
                            Mo
                          </th>
                          <th>
                            Tu
                          </th>
                          <th>
                            We
                          </th>
                          <th>
                            Th
                          </th>
                          <th>
                            Fr
                          </th>
                          <th>
                            Sa
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td
                            class="ant-picker-cell"
                            title="2016-11-27"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              27
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2016-11-28"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              28
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2016-11-29"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              29
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2016-11-30"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              30
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-01"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              1
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-02"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              2
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-03"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              3
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-04"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              4
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-05"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              5
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-06"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              6
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-07"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              7
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-08"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              8
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-09"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              9
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-10"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              10
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-11"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              11
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-12"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              12
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-13"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              13
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-14"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              14
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-15"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              15
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-16"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              16
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-17"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              17
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-18"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              18
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-19"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              19
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-20"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              20
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-21"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              21
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-22"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              22
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-23"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              23
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-24"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              24
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-25"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              25
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-26"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              26
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-27"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              27
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-28"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              28
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-29"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              29
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-30"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              30
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell ant-picker-cell-in-view"
                            title="2016-12-31"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              31
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td
                            class="ant-picker-cell"
                            title="2017-01-01"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              1
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2017-01-02"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              2
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2017-01-03"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              3
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2017-01-04"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              4
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2017-01-05"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              5
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2017-01-06"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              6
                            </div>
                          </td>
                          <td
                            class="ant-picker-cell"
                            title="2017-01-07"
                          >
                            <div
                              class="ant-picker-cell-inner"
                            >
                              7
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-picker ant-picker-outlined"
  >
    <div
      class="ant-picker-input"
    >
      <input
        aria-invalid="false"
        autocomplete="off"
        placeholder="Select month"
        size="12"
        value=""
      />
      <span
        class="ant-picker-suffix"
      >
        <span
          aria-label="calendar"
          class="anticon anticon-calendar"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="calendar"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
            />
          </svg>
        </span>
      </span>
    </div>
  </div>,
  <div
    class="ant-picker-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-picker-dropdown-placement-bottomLeft"
    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
  >
    <div
      class="ant-picker-panel-container ant-picker-month-panel-container"
      style="margin-left: 0px; margin-right: auto;"
      tabindex="-1"
    >
      <div
        class="ant-picker-panel-layout"
      >
        <div>
          <div
            class="ant-picker-panel"
            tabindex="0"
          >
            <div
              class="ant-picker-month-panel"
            >
              <div
                class="ant-picker-header"
              >
                <button
                  aria-label="Last year (Control + left)"
                  class="ant-picker-header-super-prev-btn"
                  tabindex="-1"
                  type="button"
                >
                  <span
                    class="ant-picker-super-prev-icon"
                  />
                </button>
                <div
                  class="ant-picker-header-view"
                >
                  <button
                    aria-label="Choose a year"
                    class="ant-picker-year-btn"
                    tabindex="-1"
                    type="button"
                  >
                    2016
                  </button>
                </div>
                <button
                  aria-label="Next year (Control + right)"
                  class="ant-picker-header-super-next-btn"
                  tabindex="-1"
                  type="button"
                >
                  <span
                    class="ant-picker-super-next-icon"
                  />
                </button>
              </div>
              <div
                class="ant-picker-body"
              >
                <table
                  class="ant-picker-content"
                >
                  <tbody>
                    <tr>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-01"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          Jan
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-02"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          Feb
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-03"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          Mar
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-04"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          Apr
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-05"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          May
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-06"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          Jun
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-07"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          Jul
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-08"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          Aug
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-09"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          Sep
                        </div>
                      </td>
                    </tr>
                    <tr>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-10"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          Oct
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-11"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          Nov
                        </div>
                      </td>
                      <td
                        class="ant-picker-cell ant-picker-cell-in-view"
                        title="2016-12"
                      >
                        <div
                          class="ant-picker-cell-inner"
                        >
                          Dec
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    class="ant-radio-group ant-radio-group-outline"
  >
    <label
      class="ant-radio-button-wrapper ant-radio-button-wrapper-checked"
    >
      <span
        class="ant-radio-button ant-radio-button-checked"
      >
        <input
          checked=""
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="a"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Hangzhou
      </span>
    </label>
    <label
      class="ant-radio-button-wrapper"
    >
      <span
        class="ant-radio-button"
      >
        <input
          class="ant-radio-button-input"
          name="test-id"
          type="radio"
          value="b"
        />
        <span
          class="ant-radio-button-inner"
        />
      </span>
      <span
        class="ant-radio-button-label"
      >
        Shanghai
      </span>
    </label>
  </div>,
  <div
    class="ant-select ant-select-outlined ant-select-auto-complete ant-select-single ant-select-show-search"
    style="width: 100px;"
  >
    <div
      class="ant-select-selector"
    >
      <span
        class="ant-select-selection-wrap"
      >
        <span
          class="ant-select-selection-search"
        >
          <input
            aria-autocomplete="list"
            aria-controls="rc_select_TEST_OR_SSR_list"
            aria-expanded="false"
            aria-haspopup="listbox"
            aria-owns="rc_select_TEST_OR_SSR_list"
            autocomplete="off"
            class="ant-select-selection-search-input"
            id="rc_select_TEST_OR_SSR"
            role="combobox"
            type="search"
            value=""
          />
        </span>
        <span
          class="ant-select-selection-placeholder"
        >
          input here
        </span>
      </span>
    </div>
    <div
      class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-select-dropdown-empty ant-select-dropdown-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div>
        <div
          class="ant-select-item-empty"
          id="rc_select_TEST_OR_SSR_list"
          role="listbox"
        />
      </div>
    </div>
  </div>,
  <br />,
  <span
    class="ant-input-group-wrapper ant-input-group-wrapper-outlined"
  >
    <span
      class="ant-input-wrapper ant-input-group"
    >
      <span
        class="ant-input-group-addon"
      >
        Http://
      </span>
      <span
        class="ant-input-affix-wrapper ant-input-outlined"
      >
        <span
          class="ant-input-prefix"
        >
          $
        </span>
        <input
          class="ant-input"
          type="text"
          value="mysite"
        />
      </span>
      <span
        class="ant-input-group-addon"
      >
        .com
      </span>
    </span>
  </span>,
  <span
    class="ant-input-affix-wrapper ant-input-outlined"
    style="width: 50px;"
  >
    <input
      class="ant-input"
      type="text"
      value=""
    />
    <span
      class="ant-input-suffix"
    >
      Y
    </span>
  </span>,
  <input
    class="ant-input ant-input-outlined"
    style="width: 50px;"
    type="text"
    value=""
  />,
  <span
    class="ant-input-affix-wrapper ant-input-outlined"
    style="width: 50px;"
  >
    <input
      class="ant-input"
      type="text"
      value="1"
    />
    <span
      class="ant-input-suffix"
    >
      Y
    </span>
  </span>,
]
`;

exports[`renders components/input/demo/align.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/allowClear.tsx extend context correctly 1`] = `
Array [
  <span
    class="ant-input-affix-wrapper ant-input-outlined"
  >
    <input
      class="ant-input"
      placeholder="input with clear icon"
      type="text"
      value=""
    />
    <span
      class="ant-input-suffix"
    >
      <button
        class="ant-input-clear-icon ant-input-clear-icon-hidden"
        tabindex="-1"
        type="button"
      >
        <span
          aria-label="close-circle"
          class="anticon anticon-close-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close-circle"
            fill="currentColor"
            fill-rule="evenodd"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
            />
          </svg>
        </span>
      </button>
    </span>
  </span>,
  <br />,
  <br />,
  <span
    class="ant-input-affix-wrapper ant-input-textarea-affix-wrapper ant-input-textarea-allow-clear ant-input-outlined"
  >
    <textarea
      class="ant-input"
      placeholder="textarea with clear icon"
    />
    <span
      class="ant-input-suffix"
    >
      <button
        class="ant-input-clear-icon ant-input-clear-icon-hidden"
        tabindex="-1"
        type="button"
      >
        <span
          aria-label="close-circle"
          class="anticon anticon-close-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close-circle"
            fill="currentColor"
            fill-rule="evenodd"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
            />
          </svg>
        </span>
      </button>
    </span>
  </span>,
]
`;

exports[`renders components/input/demo/allowClear.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/autosize-textarea.tsx extend context correctly 1`] = `
Array [
  <textarea
    class="ant-input ant-input-outlined"
    placeholder="Autosize height based on content lines"
    style="resize: none; height: 2px;"
  />,
  <div
    style="margin: 24px 0px;"
  />,
  <textarea
    class="ant-input ant-input-outlined"
    placeholder="Autosize height with minimum and maximum number of lines"
    style="resize: none; height: -38px; min-height: -6px; max-height: -38px;"
  />,
  <div
    style="margin: 24px 0px;"
  />,
  <textarea
    class="ant-input ant-input-outlined"
    placeholder="Controlled autosize"
    style="resize: none; height: -30px; min-height: -14px; max-height: -30px;"
  />,
]
`;

exports[`renders components/input/demo/autosize-textarea.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/basic.tsx extend context correctly 1`] = `
<input
  class="ant-input ant-input-outlined"
  placeholder="Basic usage"
  type="text"
  value=""
/>
`;

exports[`renders components/input/demo/basic.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/borderless-debug.tsx extend context correctly 1`] = `
<div
  style="background-color: rgba(0, 0, 128, 0.2);"
>
  <input
    class="ant-input ant-input-borderless"
    placeholder="Unbordered"
    type="text"
    value=""
  />
  <input
    class="ant-input ant-input-lg ant-input-borderless"
    placeholder="Unbordered"
    type="text"
    value=""
  />
  <textarea
    class="ant-input ant-input-borderless"
    placeholder="Unbordered"
  />
  <span
    class="ant-input-affix-wrapper ant-input-textarea-affix-wrapper ant-input-textarea-allow-clear ant-input-borderless"
  >
    <textarea
      class="ant-input"
      placeholder="Unbordered"
    />
    <span
      class="ant-input-suffix"
    >
      <button
        class="ant-input-clear-icon ant-input-clear-icon-hidden"
        tabindex="-1"
        type="button"
      >
        <span
          aria-label="close-circle"
          class="anticon anticon-close-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close-circle"
            fill="currentColor"
            fill-rule="evenodd"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
            />
          </svg>
        </span>
      </button>
    </span>
  </span>
  <span
    class="ant-input-affix-wrapper ant-input-borderless"
  >
    <input
      class="ant-input"
      placeholder="Unbordered"
      type="text"
      value=""
    />
    <span
      class="ant-input-suffix"
    >
      <button
        class="ant-input-clear-icon ant-input-clear-icon-hidden"
        tabindex="-1"
        type="button"
      >
        <span
          aria-label="close-circle"
          class="anticon anticon-close-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close-circle"
            fill="currentColor"
            fill-rule="evenodd"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
            />
          </svg>
        </span>
      </button>
    </span>
  </span>
  <span
    class="ant-input-affix-wrapper ant-input-borderless"
  >
    <span
      class="ant-input-prefix"
    >
      ￥
    </span>
    <input
      class="ant-input"
      type="text"
      value=""
    />
    <span
      class="ant-input-suffix"
    >
      RMB
    </span>
  </span>
  <span
    class="ant-input-affix-wrapper ant-input-disabled ant-input-affix-wrapper-disabled ant-input-borderless"
  >
    <span
      class="ant-input-prefix"
    >
      ￥
    </span>
    <input
      class="ant-input ant-input-disabled"
      disabled=""
      type="text"
      value=""
    />
    <span
      class="ant-input-suffix"
    >
      RMB
    </span>
  </span>
  <span
    class="ant-input-affix-wrapper ant-input-textarea-affix-wrapper ant-input-textarea-allow-clear ant-input-outlined"
    style="border: 2px solid #000;"
  >
    <textarea
      class="ant-input"
    />
    <span
      class="ant-input-suffix"
    >
      <button
        class="ant-input-clear-icon ant-input-clear-icon-hidden"
        tabindex="-1"
        type="button"
      >
        <span
          aria-label="close-circle"
          class="anticon anticon-close-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close-circle"
            fill="currentColor"
            fill-rule="evenodd"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
            />
          </svg>
        </span>
      </button>
    </span>
  </span>
  <input
    class="ant-input ant-input-borderless ant-input-status-error"
    type="text"
    value="error"
  />
  <input
    class="ant-input ant-input-borderless ant-input-status-warning"
    type="text"
    value="warning"
  />
  <span
    class="ant-input-affix-wrapper ant-input-borderless ant-input-status-error"
  >
    <span
      class="ant-input-prefix"
    >
      $
    </span>
    <input
      class="ant-input"
      type="text"
      value="error"
    />
  </span>
  <span
    class="ant-input-affix-wrapper ant-input-borderless ant-input-status-warning"
  >
    <span
      class="ant-input-prefix"
    >
      $
    </span>
    <input
      class="ant-input"
      type="text"
      value="warning"
    />
  </span>
</div>
`;

exports[`renders components/input/demo/borderless-debug.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/compact-style.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-middle ant-space-gap-col-middle"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
    >
      <input
        class="ant-input ant-input-outlined ant-input-compact-item ant-input-compact-first-item ant-input-compact-last-item"
        type="text"
        value="26888888"
      />
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
    >
      <input
        class="ant-input ant-input-outlined ant-input-compact-item ant-input-compact-first-item"
        style="width: 20%;"
        type="text"
        value="0571"
      />
      <input
        class="ant-input ant-input-outlined ant-input-compact-item ant-input-compact-last-item"
        style="width: 80%;"
        type="text"
        value="26888888"
      />
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
    >
      <span
        class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search ant-input-compact-item ant-input-compact-first-item ant-input-compact-last-item"
      >
        <span
          class="ant-input-wrapper ant-input-group"
        >
          <span
            class="ant-input-group-addon"
          >
            https://
          </span>
          <span
            class="ant-input-affix-wrapper ant-input-outlined"
          >
            <input
              class="ant-input"
              placeholder="input search text"
              type="search"
              value=""
            />
            <span
              class="ant-input-suffix"
            >
              <button
                class="ant-input-clear-icon ant-input-clear-icon-hidden"
                tabindex="-1"
                type="button"
              >
                <span
                  aria-label="close-circle"
                  class="anticon anticon-close-circle"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="close-circle"
                    fill="currentColor"
                    fill-rule="evenodd"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                    />
                  </svg>
                </span>
              </button>
            </span>
          </span>
          <span
            class="ant-input-group-addon"
          >
            <button
              class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-input-search-button"
              type="button"
            >
              <span
                class="ant-btn-icon"
              >
                <span
                  aria-label="search"
                  class="anticon anticon-search"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="search"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                    />
                  </svg>
                </span>
              </span>
            </button>
          </span>
        </span>
      </span>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
      style="width: 100%;"
    >
      <input
        class="ant-input ant-input-outlined ant-input-compact-item ant-input-compact-first-item"
        type="text"
        value="Combine input and button"
      />
      <button
        class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-compact-item ant-btn-compact-last-item"
        type="button"
      >
        <span>
          Submit
        </span>
      </button>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
    >
      <div
        class="ant-select ant-select-outlined ant-select-compact-item ant-select-compact-first-item ant-select-single ant-select-show-arrow"
      >
        <div
          class="ant-select-selector"
        >
          <span
            class="ant-select-selection-wrap"
          >
            <span
              class="ant-select-selection-search"
            >
              <input
                aria-autocomplete="list"
                aria-controls="rc_select_TEST_OR_SSR_list"
                aria-expanded="false"
                aria-haspopup="listbox"
                aria-owns="rc_select_TEST_OR_SSR_list"
                autocomplete="off"
                class="ant-select-selection-search-input"
                id="rc_select_TEST_OR_SSR"
                readonly=""
                role="combobox"
                style="opacity: 0;"
                type="search"
                unselectable="on"
                value=""
              />
            </span>
            <span
              class="ant-select-selection-item"
              title="Zhejiang"
            >
              Zhejiang
            </span>
          </span>
        </div>
        <div
          class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-select-dropdown-placement-bottomLeft"
          style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
        >
          <div>
            <div
              id="rc_select_TEST_OR_SSR_list"
              role="listbox"
              style="height: 0px; width: 0px; overflow: hidden;"
            >
              <div
                aria-label="Zhejiang"
                aria-selected="false"
                id="rc_select_TEST_OR_SSR_list_0"
                role="option"
              >
                zhejiang
              </div>
              <div
                aria-label="Jiangsu"
                aria-selected="false"
                id="rc_select_TEST_OR_SSR_list_1"
                role="option"
              >
                jiangsu
              </div>
            </div>
            <div
              class="rc-virtual-list"
              style="position: relative;"
            >
              <div
                class="rc-virtual-list-holder"
                style="max-height: 256px; overflow-y: auto;"
              >
                <div>
                  <div
                    class="rc-virtual-list-holder-inner"
                    style="display: flex; flex-direction: column;"
                  >
                    <div
                      aria-selected="false"
                      class="ant-select-item ant-select-item-option ant-select-item-option-active"
                      title="Zhejiang"
                    >
                      <div
                        class="ant-select-item-option-content"
                      >
                        Zhejiang
                      </div>
                      <span
                        aria-hidden="true"
                        class="ant-select-item-option-state"
                        style="user-select: none;"
                        unselectable="on"
                      />
                    </div>
                    <div
                      aria-selected="false"
                      class="ant-select-item ant-select-item-option"
                      title="Jiangsu"
                    >
                      <div
                        class="ant-select-item-option-content"
                      >
                        Jiangsu
                      </div>
                      <span
                        aria-hidden="true"
                        class="ant-select-item-option-state"
                        style="user-select: none;"
                        unselectable="on"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <span
          aria-hidden="true"
          class="ant-select-arrow"
          style="user-select: none;"
          unselectable="on"
        >
          <span
            aria-label="down"
            class="anticon anticon-down ant-select-suffix"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="down"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
              />
            </svg>
          </span>
        </span>
      </div>
      <input
        class="ant-input ant-input-outlined ant-input-compact-item ant-input-compact-last-item"
        type="text"
        value="Xihu District, Hangzhou"
      />
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space-compact"
    >
      <span
        class="ant-input-group-wrapper ant-input-group-wrapper-lg ant-input-group-wrapper-outlined ant-input-compact-item ant-input-compact-first-item"
      >
        <span
          class="ant-input-wrapper ant-input-group"
        >
          <span
            class="ant-input-group-addon"
          >
            <span
              aria-label="search"
              class="anticon anticon-search"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="search"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                />
              </svg>
            </span>
          </span>
          <input
            class="ant-input ant-input-lg ant-input-outlined"
            placeholder="large size"
            type="text"
            value=""
          />
        </span>
      </span>
      <input
        class="ant-input ant-input-lg ant-input-outlined ant-input-compact-item ant-input-compact-last-item"
        placeholder="another input"
        type="text"
        value=""
      />
    </div>
  </div>
</div>
`;

exports[`renders components/input/demo/compact-style.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/debug-addon.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small"
>
  <div
    class="ant-space-item"
  >
    Input addon Button:
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-outlined"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <input
          class="ant-input ant-input-outlined"
          type="text"
          value="mysite"
        />
        <span
          class="ant-input-group-addon"
        >
          <button
            class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
            type="button"
          >
            <span>
              Submit
            </span>
          </button>
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-outlined"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <input
          class="ant-input ant-input-outlined"
          type="text"
          value="mysite"
        />
        <span
          class="ant-input-group-addon"
        >
          <button
            class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
            type="button"
          >
            <span>
              Submit
            </span>
          </button>
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-space-item"
  >
    <br />
  </div>
  <div
    class="ant-space-item"
  >
    <br />
  </div>
  <div
    class="ant-space-item"
  >
    Input addon Button icon:
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-outlined"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <input
          class="ant-input ant-input-outlined"
          type="text"
          value="mysite"
        />
        <span
          class="ant-input-group-addon"
        >
          <button
            class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
            type="button"
          >
            <span
              aria-label="setting"
              class="anticon anticon-setting"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="setting"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M924.8 625.7l-65.5-56c3.1-19 4.7-38.4 4.7-57.8s-1.6-38.8-4.7-57.8l65.5-56a32.03 32.03 0 009.3-35.2l-.9-2.6a443.74 443.74 0 00-79.7-137.9l-1.8-2.1a32.12 32.12 0 00-35.1-9.5l-81.3 28.9c-30-24.6-63.5-44-99.7-57.6l-15.7-85a32.05 32.05 0 00-25.8-25.7l-2.7-.5c-52.1-9.4-106.9-9.4-159 0l-2.7.5a32.05 32.05 0 00-25.8 25.7l-15.8 85.4a351.86 351.86 0 00-99 57.4l-81.9-29.1a32 32 0 00-35.1 9.5l-1.8 2.1a446.02 446.02 0 00-79.7 137.9l-.9 2.6c-4.5 12.5-.8 26.5 9.3 35.2l66.3 56.6c-3.1 18.8-4.6 38-4.6 57.1 0 19.2 1.5 38.4 4.6 57.1L99 625.5a32.03 32.03 0 00-9.3 35.2l.9 2.6c18.1 50.4 44.9 96.9 79.7 137.9l1.8 2.1a32.12 32.12 0 0035.1 9.5l81.9-29.1c29.8 24.5 63.1 43.9 99 57.4l15.8 85.4a32.05 32.05 0 0025.8 25.7l2.7.5a449.4 449.4 0 00159 0l2.7-.5a32.05 32.05 0 0025.8-25.7l15.7-85a350 350 0 0099.7-57.6l81.3 28.9a32 32 0 0035.1-9.5l1.8-2.1c34.8-41.1 61.6-87.5 79.7-137.9l.9-2.6c4.5-12.3.8-26.3-9.3-35zM788.3 465.9c2.5 15.1 3.8 30.6 3.8 46.1s-1.3 31-3.8 46.1l-6.6 40.1 74.7 63.9a370.03 370.03 0 01-42.6 73.6L721 702.8l-31.4 25.8c-23.9 19.6-50.5 35-79.3 45.8l-38.1 14.3-17.9 97a377.5 377.5 0 01-85 0l-17.9-97.2-37.8-14.5c-28.5-10.8-55-26.2-78.7-45.7l-31.4-25.9-93.4 33.2c-17-22.9-31.2-47.6-42.6-73.6l75.5-64.5-6.5-40c-2.4-14.9-3.7-30.3-3.7-45.5 0-15.3 1.2-30.6 3.7-45.5l6.5-40-75.5-64.5c11.3-26.1 25.6-50.7 42.6-73.6l93.4 33.2 31.4-25.9c23.7-19.5 50.2-34.9 78.7-45.7l37.9-14.3 17.9-97.2c28.1-3.2 56.8-3.2 85 0l17.9 97 38.1 14.3c28.7 10.8 55.4 26.2 79.3 45.8l31.4 25.8 92.8-32.9c17 22.9 31.2 47.6 42.6 73.6L781.8 426l6.5 39.9zM512 326c-97.2 0-176 78.8-176 176s78.8 176 176 176 176-78.8 176-176-78.8-176-176-176zm79.2 255.2A111.6 111.6 0 01512 614c-29.9 0-58-11.7-79.2-32.8A111.6 111.6 0 01400 502c0-29.9 11.7-58 32.8-79.2C454 401.6 482.1 390 512 390c29.9 0 58 11.6 79.2 32.8A111.6 111.6 0 01624 502c0 29.9-11.7 58-32.8 79.2z"
                />
              </svg>
            </span>
          </button>
        </span>
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/input/demo/debug-addon.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/filled-debug.tsx extend context correctly 1`] = `
<div
  class="ant-flex ant-flex-align-stretch ant-flex-vertical"
  style="gap: 20px;"
>
  <div
    class="ant-flex"
    style="gap: 12px;"
  >
    <input
      class="ant-input ant-input-filled"
      placeholder="Filled"
      type="text"
      value=""
    />
    <input
      class="ant-input ant-input-disabled ant-input-filled"
      disabled=""
      placeholder="Filled"
      type="text"
      value=""
    />
    <input
      class="ant-input ant-input-filled ant-input-status-error"
      placeholder="Filled"
      type="text"
      value="Filled Error"
    />
  </div>
  <div
    class="ant-flex"
    style="gap: 12px;"
  >
    <span
      class="ant-input-affix-wrapper ant-input-filled"
    >
      <span
        class="ant-input-prefix"
      >
        $
      </span>
      <input
        class="ant-input"
        placeholder="Filled"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-input-affix-wrapper ant-input-disabled ant-input-affix-wrapper-disabled ant-input-filled"
    >
      <span
        class="ant-input-prefix"
      >
        $
      </span>
      <input
        class="ant-input ant-input-disabled"
        disabled=""
        placeholder="Filled"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-input-affix-wrapper ant-input-filled ant-input-status-error"
    >
      <span
        class="ant-input-prefix"
      >
        $
      </span>
      <input
        class="ant-input"
        placeholder="Filled"
        type="text"
        value="Filled Error"
      />
    </span>
  </div>
  <div
    class="ant-flex"
    style="gap: 12px;"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-filled"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <span
          class="ant-input-group-addon"
        >
          http://
        </span>
        <input
          class="ant-input ant-input-filled"
          placeholder="Filled"
          type="text"
          value=""
        />
        <span
          class="ant-input-group-addon"
        >
          .com
        </span>
      </span>
    </span>
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-disabled ant-input-group-wrapper-filled"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <span
          class="ant-input-group-addon"
        >
          http://
        </span>
        <input
          class="ant-input ant-input-disabled ant-input-filled"
          disabled=""
          placeholder="Filled"
          type="text"
          value=""
        />
        <span
          class="ant-input-group-addon"
        >
          .com
        </span>
      </span>
    </span>
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-filled ant-input-group-wrapper-status-error"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <span
          class="ant-input-group-addon"
        >
          http://
        </span>
        <input
          class="ant-input ant-input-filled ant-input-status-error"
          placeholder="Filled"
          type="text"
          value="Filled Error"
        />
        <span
          class="ant-input-group-addon"
        >
          .com
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-flex"
    style="gap: 12px;"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-filled"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <input
          class="ant-input ant-input-filled"
          placeholder="Filled"
          type="text"
          value=""
        />
        <span
          class="ant-input-group-addon"
        >
          .com
        </span>
      </span>
    </span>
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-disabled ant-input-group-wrapper-filled"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <input
          class="ant-input ant-input-disabled ant-input-filled"
          disabled=""
          placeholder="Filled"
          type="text"
          value=""
        />
        <span
          class="ant-input-group-addon"
        >
          .com
        </span>
      </span>
    </span>
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-filled ant-input-group-wrapper-status-error"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <input
          class="ant-input ant-input-filled ant-input-status-error"
          placeholder="Filled"
          type="text"
          value="Filled Error"
        />
        <span
          class="ant-input-group-addon"
        >
          .com
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-flex"
    style="gap: 12px;"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-filled"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <span
          class="ant-input-group-addon"
        >
          http://
        </span>
        <input
          class="ant-input ant-input-filled"
          placeholder="Filled"
          type="text"
          value=""
        />
      </span>
    </span>
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-disabled ant-input-group-wrapper-filled"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <span
          class="ant-input-group-addon"
        >
          http://
        </span>
        <input
          class="ant-input ant-input-disabled ant-input-filled"
          disabled=""
          placeholder="Filled"
          type="text"
          value=""
        />
      </span>
    </span>
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-filled ant-input-group-wrapper-status-error"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <span
          class="ant-input-group-addon"
        >
          http://
        </span>
        <input
          class="ant-input ant-input-filled ant-input-status-error"
          placeholder="Filled"
          type="text"
          value="Filled Error"
        />
      </span>
    </span>
  </div>
  <textarea
    class="ant-input ant-input-filled"
    placeholder="Basic"
  />
  <textarea
    class="ant-input ant-input-filled ant-input-status-error"
    placeholder="Basic"
  >
    Filled Error
  </textarea>
  <span
    class="ant-input-affix-wrapper ant-input-textarea-affix-wrapper ant-input-textarea-allow-clear ant-input-filled"
  >
    <textarea
      class="ant-input"
      placeholder="Allow Clear"
    />
    <span
      class="ant-input-suffix"
    >
      <button
        class="ant-input-clear-icon ant-input-clear-icon-hidden"
        tabindex="-1"
        type="button"
      >
        <span
          aria-label="close-circle"
          class="anticon anticon-close-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close-circle"
            fill="currentColor"
            fill-rule="evenodd"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
            />
          </svg>
        </span>
      </button>
    </span>
  </span>
  <span
    class="ant-input-affix-wrapper ant-input-textarea-affix-wrapper ant-input-textarea-show-count ant-input-show-count ant-input-filled"
    data-count="0"
  >
    <textarea
      class="ant-input"
      placeholder="Show Count"
    />
    <span
      class="ant-input-suffix"
    >
      <span
        class="ant-input-data-count"
      >
        0
      </span>
    </span>
  </span>
  <span
    class="ant-input-affix-wrapper ant-input-textarea-affix-wrapper ant-input-textarea-show-count ant-input-show-count ant-input-filled ant-input-status-error"
    data-count="12"
  >
    <textarea
      class="ant-input"
      placeholder="Show Count"
    >
      Filled Error
    </textarea>
    <span
      class="ant-input-suffix"
    >
      <span
        class="ant-input-data-count"
      >
        12
      </span>
    </span>
  </span>
</div>
`;

exports[`renders components/input/demo/filled-debug.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/focus.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small"
  style="width: 100%;"
>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small"
      style="flex-wrap: wrap;"
    >
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
          type="button"
        >
          <span>
            Focus at first
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
          type="button"
        >
          <span>
            Focus at last
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
          type="button"
        >
          <span>
            Focus to select all
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
          type="button"
        >
          <span>
            Focus prevent scroll
          </span>
        </button>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          aria-checked="true"
          class="ant-switch ant-switch-checked"
          role="switch"
          type="button"
        >
          <div
            class="ant-switch-handle"
          />
          <span
            class="ant-switch-inner"
          >
            <span
              class="ant-switch-inner-checked"
            >
              Input
            </span>
            <span
              class="ant-switch-inner-unchecked"
            >
              TextArea
            </span>
          </span>
        </button>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <br />
  </div>
  <div
    class="ant-space-item"
  >
    <input
      class="ant-input ant-input-outlined"
      style="width: 100%;"
      type="text"
      value="Ant Design love you!"
    />
  </div>
</div>
`;

exports[`renders components/input/demo/focus.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/group.tsx extend context correctly 1`] = `
<div
  class="site-input-group-wrapper"
>
  <span
    class="ant-input-group ant-input-group-lg"
  >
    <div
      class="ant-row"
      style="margin-left: -4px; margin-right: -4px;"
    >
      <div
        class="ant-col ant-col-5"
        style="padding-left: 4px; padding-right: 4px;"
      >
        <input
          class="ant-input ant-input-outlined"
          type="text"
          value="0571"
        />
      </div>
      <div
        class="ant-col ant-col-8"
        style="padding-left: 4px; padding-right: 4px;"
      >
        <input
          class="ant-input ant-input-outlined"
          type="text"
          value="26888888"
        />
      </div>
    </div>
  </span>
  <br />
  <span
    class="ant-input-group ant-input-group-compact"
  >
    <input
      class="ant-input ant-input-outlined"
      style="width: 20%;"
      type="text"
      value="0571"
    />
    <input
      class="ant-input ant-input-outlined"
      style="width: 30%;"
      type="text"
      value="26888888"
    />
  </span>
  <br />
  <span
    class="ant-input-group ant-input-group-compact"
  >
    <input
      class="ant-input ant-input-outlined"
      style="width: calc(100% - 200px);"
      type="text"
      value="https://ant.design"
    />
    <button
      class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
      type="button"
    >
      <span>
        Submit
      </span>
    </button>
  </span>
  <br />
  <span
    class="ant-input-group ant-input-group-compact"
  >
    <input
      class="ant-input ant-input-outlined"
      style="width: calc(100% - 200px);"
      type="text"
      value="**************:ant-design/ant-design.git"
    />
    <button
      aria-describedby="test-id"
      class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only"
      type="button"
    >
      <span
        class="ant-btn-icon"
      >
        <span
          aria-label="search"
          class="anticon anticon-search"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="search"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
            />
          </svg>
        </span>
      </span>
    </button>
    <div
      class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast ant-tooltip-placement-top"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div
        class="ant-tooltip-arrow"
        style="position: absolute; bottom: 0px; left: 0px;"
      />
      <div
        class="ant-tooltip-content"
      >
        <div
          class="ant-tooltip-inner"
          id="test-id"
          role="tooltip"
        >
          search git url
        </div>
      </div>
    </div>
  </span>
  <br />
  <span
    class="ant-input-group ant-input-group-compact"
  >
    <div
      class="ant-select ant-select-outlined ant-select-single ant-select-show-arrow"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="rc_select_TEST_OR_SSR_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="rc_select_TEST_OR_SSR_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="rc_select_TEST_OR_SSR"
              readonly=""
              role="combobox"
              style="opacity: 0;"
              type="search"
              unselectable="on"
              value=""
            />
          </span>
          <span
            class="ant-select-selection-item"
            title="Zhejiang"
          >
            Zhejiang
          </span>
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
      >
        <div>
          <div
            id="rc_select_TEST_OR_SSR_list"
            role="listbox"
            style="height: 0px; width: 0px; overflow: hidden;"
          >
            <div
              aria-label="Zhejiang"
              aria-selected="true"
              id="rc_select_TEST_OR_SSR_list_0"
              role="option"
            >
              Zhejiang
            </div>
            <div
              aria-label="Jiangsu"
              aria-selected="false"
              id="rc_select_TEST_OR_SSR_list_1"
              role="option"
            >
              Jiangsu
            </div>
          </div>
          <div
            class="rc-virtual-list"
            style="position: relative;"
          >
            <div
              class="rc-virtual-list-holder"
              style="max-height: 256px; overflow-y: auto;"
            >
              <div>
                <div
                  class="rc-virtual-list-holder-inner"
                  style="display: flex; flex-direction: column;"
                >
                  <div
                    aria-selected="true"
                    class="ant-select-item ant-select-item-option ant-select-item-option-active ant-select-item-option-selected"
                    title="Zhejiang"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Zhejiang
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                  <div
                    aria-selected="false"
                    class="ant-select-item ant-select-item-option"
                    title="Jiangsu"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Jiangsu
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select: none;"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <input
      class="ant-input ant-input-outlined"
      style="width: 50%;"
      type="text"
      value="Xihu District, Hangzhou"
    />
  </span>
  <br />
  <span
    class="ant-input-group ant-input-group-compact"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search"
      style="width: 40%;"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <span
          class="ant-input-affix-wrapper ant-input-outlined"
        >
          <input
            class="ant-input"
            type="search"
            value="0571"
          />
          <span
            class="ant-input-suffix"
          >
            <button
              class="ant-input-clear-icon"
              tabindex="-1"
              type="button"
            >
              <span
                aria-label="close-circle"
                class="anticon anticon-close-circle"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="close-circle"
                  fill="currentColor"
                  fill-rule="evenodd"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                  />
                </svg>
              </span>
            </button>
          </span>
        </span>
        <span
          class="ant-input-group-addon"
        >
          <button
            class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-input-search-button"
            type="button"
          >
            <span
              class="ant-btn-icon"
            >
              <span
                aria-label="search"
                class="anticon anticon-search"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="search"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                  />
                </svg>
              </span>
            </span>
          </button>
        </span>
      </span>
    </span>
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search"
      style="width: 40%;"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <span
          class="ant-input-affix-wrapper ant-input-outlined"
        >
          <input
            class="ant-input"
            type="search"
            value="26888888"
          />
          <span
            class="ant-input-suffix"
          >
            <button
              class="ant-input-clear-icon"
              tabindex="-1"
              type="button"
            >
              <span
                aria-label="close-circle"
                class="anticon anticon-close-circle"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="close-circle"
                  fill="currentColor"
                  fill-rule="evenodd"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                  />
                </svg>
              </span>
            </button>
          </span>
        </span>
        <span
          class="ant-input-group-addon"
        >
          <button
            class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-input-search-button"
            type="button"
          >
            <span
              class="ant-btn-icon"
            >
              <span
                aria-label="search"
                class="anticon anticon-search"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="search"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                  />
                </svg>
              </span>
            </span>
          </button>
        </span>
      </span>
    </span>
  </span>
  <br />
  <span
    class="ant-input-group ant-input-group-compact"
  >
    <div
      class="ant-select ant-select-outlined ant-select-single ant-select-show-arrow"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="rc_select_TEST_OR_SSR_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="rc_select_TEST_OR_SSR_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="rc_select_TEST_OR_SSR"
              readonly=""
              role="combobox"
              style="opacity: 0;"
              type="search"
              unselectable="on"
              value=""
            />
          </span>
          <span
            class="ant-select-selection-item"
            title="Option1"
          >
            Option1
          </span>
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
      >
        <div>
          <div
            id="rc_select_TEST_OR_SSR_list"
            role="listbox"
            style="height: 0px; width: 0px; overflow: hidden;"
          >
            <div
              aria-label="Option1"
              aria-selected="true"
              id="rc_select_TEST_OR_SSR_list_0"
              role="option"
            >
              Option1
            </div>
            <div
              aria-label="Option2"
              aria-selected="false"
              id="rc_select_TEST_OR_SSR_list_1"
              role="option"
            >
              Option2
            </div>
          </div>
          <div
            class="rc-virtual-list"
            style="position: relative;"
          >
            <div
              class="rc-virtual-list-holder"
              style="max-height: 256px; overflow-y: auto;"
            >
              <div>
                <div
                  class="rc-virtual-list-holder-inner"
                  style="display: flex; flex-direction: column;"
                >
                  <div
                    aria-selected="true"
                    class="ant-select-item ant-select-item-option ant-select-item-option-active ant-select-item-option-selected"
                    title="Option1"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Option1
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                  <div
                    aria-selected="false"
                    class="ant-select-item ant-select-item-option"
                    title="Option2"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Option2
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select: none;"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <input
      class="ant-input ant-input-outlined"
      style="width: 50%;"
      type="text"
      value="input content"
    />
    <div
      class="ant-input-number-affix-wrapper ant-input-number-outlined"
    >
      <span
        class="ant-input-number-prefix"
      >
        @
      </span>
      <div
        class="ant-input-number"
      >
        <div
          class="ant-input-number-handler-wrap"
        >
          <span
            aria-disabled="false"
            aria-label="Increase Value"
            class="ant-input-number-handler ant-input-number-handler-up"
            role="button"
            unselectable="on"
          >
            <span
              aria-label="up"
              class="anticon anticon-up ant-input-number-handler-up-inner"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="up"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                />
              </svg>
            </span>
          </span>
          <span
            aria-disabled="false"
            aria-label="Decrease Value"
            class="ant-input-number-handler ant-input-number-handler-down"
            role="button"
            unselectable="on"
          >
            <span
              aria-label="down"
              class="anticon anticon-down ant-input-number-handler-down-inner"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="down"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                />
              </svg>
            </span>
          </span>
        </div>
        <div
          class="ant-input-number-input-wrap"
        >
          <input
            autocomplete="off"
            class="ant-input-number-input"
            role="spinbutton"
            step="1"
            value=""
          />
        </div>
      </div>
    </div>
  </span>
  <br />
  <span
    class="ant-input-group ant-input-group-compact"
  >
    <input
      class="ant-input ant-input-outlined"
      style="width: 50%;"
      type="text"
      value="input content"
    />
    <div
      class="ant-picker ant-picker-outlined"
      style="width: 50%;"
    >
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          placeholder="Select date"
          size="12"
          value=""
        />
        <span
          class="ant-picker-suffix"
        >
          <span
            aria-label="calendar"
            class="anticon anticon-calendar"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="calendar"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
              />
            </svg>
          </span>
        </span>
      </div>
    </div>
    <div
      class="ant-picker-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-picker-dropdown-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div
        class="ant-picker-panel-container ant-picker-date-panel-container"
        style="margin-left: 0px; margin-right: auto;"
        tabindex="-1"
      >
        <div
          class="ant-picker-panel-layout"
        >
          <div>
            <div
              class="ant-picker-panel"
              tabindex="0"
            >
              <div
                class="ant-picker-date-panel"
              >
                <div
                  class="ant-picker-header"
                >
                  <button
                    aria-label="Last year (Control + left)"
                    class="ant-picker-header-super-prev-btn"
                    tabindex="-1"
                    type="button"
                  >
                    <span
                      class="ant-picker-super-prev-icon"
                    />
                  </button>
                  <button
                    aria-label="Previous month (PageUp)"
                    class="ant-picker-header-prev-btn"
                    tabindex="-1"
                    type="button"
                  >
                    <span
                      class="ant-picker-prev-icon"
                    />
                  </button>
                  <div
                    class="ant-picker-header-view"
                  >
                    <button
                      aria-label="Choose a month"
                      class="ant-picker-month-btn"
                      tabindex="-1"
                      type="button"
                    >
                      Nov
                    </button>
                    <button
                      aria-label="Choose a year"
                      class="ant-picker-year-btn"
                      tabindex="-1"
                      type="button"
                    >
                      2016
                    </button>
                  </div>
                  <button
                    aria-label="Next month (PageDown)"
                    class="ant-picker-header-next-btn"
                    tabindex="-1"
                    type="button"
                  >
                    <span
                      class="ant-picker-next-icon"
                    />
                  </button>
                  <button
                    aria-label="Next year (Control + right)"
                    class="ant-picker-header-super-next-btn"
                    tabindex="-1"
                    type="button"
                  >
                    <span
                      class="ant-picker-super-next-icon"
                    />
                  </button>
                </div>
                <div
                  class="ant-picker-body"
                >
                  <table
                    class="ant-picker-content"
                  >
                    <thead>
                      <tr>
                        <th>
                          Su
                        </th>
                        <th>
                          Mo
                        </th>
                        <th>
                          Tu
                        </th>
                        <th>
                          We
                        </th>
                        <th>
                          Th
                        </th>
                        <th>
                          Fr
                        </th>
                        <th>
                          Sa
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td
                          class="ant-picker-cell"
                          title="2016-10-30"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            30
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell"
                          title="2016-10-31"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            31
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-01"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            1
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-02"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            2
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-03"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            3
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-04"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            4
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-05"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            5
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-06"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            6
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-07"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            7
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-08"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            8
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-09"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            9
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-10"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            10
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-11"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            11
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-12"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            12
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-13"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            13
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-14"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            14
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-15"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            15
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-16"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            16
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-17"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            17
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-18"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            18
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-19"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            19
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-20"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            20
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-21"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            21
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view ant-picker-cell-today"
                          title="2016-11-22"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            22
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-23"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            23
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-24"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            24
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-25"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            25
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-26"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            26
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-27"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            27
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-28"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            28
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-29"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            29
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell ant-picker-cell-in-view"
                          title="2016-11-30"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            30
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell"
                          title="2016-12-01"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            1
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell"
                          title="2016-12-02"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            2
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell"
                          title="2016-12-03"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            3
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td
                          class="ant-picker-cell"
                          title="2016-12-04"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            4
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell"
                          title="2016-12-05"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            5
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell"
                          title="2016-12-06"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            6
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell"
                          title="2016-12-07"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            7
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell"
                          title="2016-12-08"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            8
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell"
                          title="2016-12-09"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            9
                          </div>
                        </td>
                        <td
                          class="ant-picker-cell"
                          title="2016-12-10"
                        >
                          <div
                            class="ant-picker-cell-inner"
                          >
                            10
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
            <div
              class="ant-picker-footer"
            >
              <ul
                class="ant-picker-ranges"
              >
                <li
                  class="ant-picker-now"
                >
                  <a
                    aria-disabled="false"
                    class="ant-picker-now-btn"
                  >
                    Today
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </span>
  <br />
  <span
    class="ant-input-group ant-input-group-compact"
  >
    <input
      class="ant-input ant-input-outlined"
      style="width: 30%;"
      type="text"
      value="input content"
    />
    <div
      class="ant-picker ant-picker-range ant-picker-outlined"
      style="width: 70%;"
    >
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          date-range="start"
          placeholder="Start date"
          size="12"
          value=""
        />
      </div>
      <div
        class="ant-picker-range-separator"
      >
        <span
          aria-label="to"
          class="ant-picker-separator"
        >
          <span
            aria-label="swap-right"
            class="anticon anticon-swap-right"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="swap-right"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="0 0 1024 1024"
              width="1em"
            >
              <path
                d="M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z"
              />
            </svg>
          </span>
        </span>
      </div>
      <div
        class="ant-picker-input"
      >
        <input
          aria-invalid="false"
          autocomplete="off"
          date-range="end"
          placeholder="End date"
          size="12"
          value=""
        />
      </div>
      <div
        class="ant-picker-active-bar"
        style="position: absolute; width: 0px;"
      />
      <span
        class="ant-picker-suffix"
      >
        <span
          aria-label="calendar"
          class="anticon anticon-calendar"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="calendar"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-picker-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-picker-dropdown-range ant-picker-dropdown-placement-bottomLeft"
      style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
    >
      <div
        class="ant-picker-range-wrapper ant-picker-date-range-wrapper"
      >
        <div
          class="ant-picker-range-arrow"
          style="left: 0px;"
        />
        <div
          class="ant-picker-panel-container ant-picker-date-panel-container"
          style="margin-left: 0px; margin-right: auto;"
          tabindex="-1"
        >
          <div
            class="ant-picker-panel-layout"
          >
            <div>
              <div
                class="ant-picker-panels"
              >
                <div
                  class="ant-picker-panel"
                  tabindex="0"
                >
                  <div
                    class="ant-picker-date-panel"
                  >
                    <div
                      class="ant-picker-header"
                    >
                      <button
                        aria-label="Last year (Control + left)"
                        class="ant-picker-header-super-prev-btn"
                        tabindex="-1"
                        type="button"
                      >
                        <span
                          class="ant-picker-super-prev-icon"
                        />
                      </button>
                      <button
                        aria-label="Previous month (PageUp)"
                        class="ant-picker-header-prev-btn"
                        tabindex="-1"
                        type="button"
                      >
                        <span
                          class="ant-picker-prev-icon"
                        />
                      </button>
                      <div
                        class="ant-picker-header-view"
                      >
                        <button
                          aria-label="Choose a month"
                          class="ant-picker-month-btn"
                          tabindex="-1"
                          type="button"
                        >
                          Nov
                        </button>
                        <button
                          aria-label="Choose a year"
                          class="ant-picker-year-btn"
                          tabindex="-1"
                          type="button"
                        >
                          2016
                        </button>
                      </div>
                      <button
                        aria-label="Next month (PageDown)"
                        class="ant-picker-header-next-btn"
                        style="visibility: hidden;"
                        tabindex="-1"
                        type="button"
                      >
                        <span
                          class="ant-picker-next-icon"
                        />
                      </button>
                      <button
                        aria-label="Next year (Control + right)"
                        class="ant-picker-header-super-next-btn"
                        style="visibility: hidden;"
                        tabindex="-1"
                        type="button"
                      >
                        <span
                          class="ant-picker-super-next-icon"
                        />
                      </button>
                    </div>
                    <div
                      class="ant-picker-body"
                    >
                      <table
                        class="ant-picker-content"
                      >
                        <thead>
                          <tr>
                            <th>
                              Su
                            </th>
                            <th>
                              Mo
                            </th>
                            <th>
                              Tu
                            </th>
                            <th>
                              We
                            </th>
                            <th>
                              Th
                            </th>
                            <th>
                              Fr
                            </th>
                            <th>
                              Sa
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td
                              class="ant-picker-cell"
                              title="2016-10-30"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                30
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2016-10-31"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                31
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-01"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                1
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-02"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                2
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-03"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                3
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-04"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                4
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-05"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                5
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-06"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                6
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-07"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                7
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-08"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                8
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-09"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                9
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-10"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                10
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-11"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                11
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-12"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                12
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-13"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                13
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-14"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                14
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-15"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                15
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-16"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                16
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-17"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                17
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-18"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                18
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-19"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                19
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-20"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                20
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-21"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                21
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view ant-picker-cell-today"
                              title="2016-11-22"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                22
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-23"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                23
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-24"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                24
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-25"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                25
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-26"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                26
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-27"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                27
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-28"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                28
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-29"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                29
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-11-30"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                30
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2016-12-01"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                1
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2016-12-02"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                2
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2016-12-03"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                3
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td
                              class="ant-picker-cell"
                              title="2016-12-04"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                4
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2016-12-05"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                5
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2016-12-06"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                6
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2016-12-07"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                7
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2016-12-08"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                8
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2016-12-09"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                9
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2016-12-10"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                10
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
                <div
                  class="ant-picker-panel"
                  tabindex="0"
                >
                  <div
                    class="ant-picker-date-panel"
                  >
                    <div
                      class="ant-picker-header"
                    >
                      <button
                        aria-label="Last year (Control + left)"
                        class="ant-picker-header-super-prev-btn"
                        style="visibility: hidden;"
                        tabindex="-1"
                        type="button"
                      >
                        <span
                          class="ant-picker-super-prev-icon"
                        />
                      </button>
                      <button
                        aria-label="Previous month (PageUp)"
                        class="ant-picker-header-prev-btn"
                        style="visibility: hidden;"
                        tabindex="-1"
                        type="button"
                      >
                        <span
                          class="ant-picker-prev-icon"
                        />
                      </button>
                      <div
                        class="ant-picker-header-view"
                      >
                        <button
                          aria-label="Choose a month"
                          class="ant-picker-month-btn"
                          tabindex="-1"
                          type="button"
                        >
                          Dec
                        </button>
                        <button
                          aria-label="Choose a year"
                          class="ant-picker-year-btn"
                          tabindex="-1"
                          type="button"
                        >
                          2016
                        </button>
                      </div>
                      <button
                        aria-label="Next month (PageDown)"
                        class="ant-picker-header-next-btn"
                        tabindex="-1"
                        type="button"
                      >
                        <span
                          class="ant-picker-next-icon"
                        />
                      </button>
                      <button
                        aria-label="Next year (Control + right)"
                        class="ant-picker-header-super-next-btn"
                        tabindex="-1"
                        type="button"
                      >
                        <span
                          class="ant-picker-super-next-icon"
                        />
                      </button>
                    </div>
                    <div
                      class="ant-picker-body"
                    >
                      <table
                        class="ant-picker-content"
                      >
                        <thead>
                          <tr>
                            <th>
                              Su
                            </th>
                            <th>
                              Mo
                            </th>
                            <th>
                              Tu
                            </th>
                            <th>
                              We
                            </th>
                            <th>
                              Th
                            </th>
                            <th>
                              Fr
                            </th>
                            <th>
                              Sa
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td
                              class="ant-picker-cell"
                              title="2016-11-27"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                27
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2016-11-28"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                28
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2016-11-29"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                29
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2016-11-30"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                30
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-01"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                1
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-02"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                2
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-03"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                3
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-04"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                4
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-05"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                5
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-06"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                6
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-07"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                7
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-08"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                8
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-09"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                9
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-10"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                10
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-11"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                11
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-12"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                12
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-13"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                13
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-14"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                14
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-15"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                15
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-16"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                16
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-17"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                17
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-18"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                18
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-19"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                19
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-20"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                20
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-21"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                21
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-22"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                22
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-23"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                23
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-24"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                24
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-25"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                25
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-26"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                26
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-27"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                27
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-28"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                28
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-29"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                29
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-30"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                30
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell ant-picker-cell-in-view"
                              title="2016-12-31"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                31
                              </div>
                            </td>
                          </tr>
                          <tr>
                            <td
                              class="ant-picker-cell"
                              title="2017-01-01"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                1
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2017-01-02"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                2
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2017-01-03"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                3
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2017-01-04"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                4
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2017-01-05"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                5
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2017-01-06"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                6
                              </div>
                            </td>
                            <td
                              class="ant-picker-cell"
                              title="2017-01-07"
                            >
                              <div
                                class="ant-picker-cell-inner"
                              >
                                7
                              </div>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </span>
  <br />
  <span
    class="ant-input-group ant-input-group-compact"
  >
    <div
      class="ant-select ant-select-outlined ant-select-single ant-select-show-arrow"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="rc_select_TEST_OR_SSR_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="rc_select_TEST_OR_SSR_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="rc_select_TEST_OR_SSR"
              readonly=""
              role="combobox"
              style="opacity: 0;"
              type="search"
              unselectable="on"
              value=""
            />
          </span>
          <span
            class="ant-select-selection-item"
            title="Option1-1"
          >
            Option1-1
          </span>
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
      >
        <div>
          <div
            id="rc_select_TEST_OR_SSR_list"
            role="listbox"
            style="height: 0px; width: 0px; overflow: hidden;"
          >
            <div
              aria-label="Option1-1"
              aria-selected="true"
              id="rc_select_TEST_OR_SSR_list_0"
              role="option"
            >
              Option1-1
            </div>
            <div
              aria-label="Option1-2"
              aria-selected="false"
              id="rc_select_TEST_OR_SSR_list_1"
              role="option"
            >
              Option1-2
            </div>
          </div>
          <div
            class="rc-virtual-list"
            style="position: relative;"
          >
            <div
              class="rc-virtual-list-holder"
              style="max-height: 256px; overflow-y: auto;"
            >
              <div>
                <div
                  class="rc-virtual-list-holder-inner"
                  style="display: flex; flex-direction: column;"
                >
                  <div
                    aria-selected="true"
                    class="ant-select-item ant-select-item-option ant-select-item-option-active ant-select-item-option-selected"
                    title="Option1-1"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Option1-1
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                  <div
                    aria-selected="false"
                    class="ant-select-item ant-select-item-option"
                    title="Option1-2"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Option1-2
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select: none;"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-select ant-select-outlined ant-select-single ant-select-show-arrow"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="rc_select_TEST_OR_SSR_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="rc_select_TEST_OR_SSR_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="rc_select_TEST_OR_SSR"
              readonly=""
              role="combobox"
              style="opacity: 0;"
              type="search"
              unselectable="on"
              value=""
            />
          </span>
          <span
            class="ant-select-selection-item"
            title="Option2-2"
          >
            Option2-2
          </span>
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
      >
        <div>
          <div
            id="rc_select_TEST_OR_SSR_list"
            role="listbox"
            style="height: 0px; width: 0px; overflow: hidden;"
          >
            <div
              aria-label="Option2-1"
              aria-selected="false"
              id="rc_select_TEST_OR_SSR_list_0"
              role="option"
            >
              Option2-1
            </div>
            <div
              aria-label="Option2-2"
              aria-selected="true"
              id="rc_select_TEST_OR_SSR_list_1"
              role="option"
            >
              Option2-2
            </div>
          </div>
          <div
            class="rc-virtual-list"
            style="position: relative;"
          >
            <div
              class="rc-virtual-list-holder"
              style="max-height: 256px; overflow-y: auto;"
            >
              <div>
                <div
                  class="rc-virtual-list-holder-inner"
                  style="display: flex; flex-direction: column;"
                >
                  <div
                    aria-selected="false"
                    class="ant-select-item ant-select-item-option ant-select-item-option-active"
                    title="Option2-1"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Option2-1
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                  <div
                    aria-selected="true"
                    class="ant-select-item ant-select-item-option ant-select-item-option-selected"
                    title="Option2-2"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Option2-2
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select: none;"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
  </span>
  <br />
  <span
    class="ant-input-group ant-input-group-compact"
  >
    <div
      class="ant-select ant-select-outlined ant-select-single ant-select-show-arrow"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="rc_select_TEST_OR_SSR_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="rc_select_TEST_OR_SSR_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="rc_select_TEST_OR_SSR"
              readonly=""
              role="combobox"
              style="opacity: 0;"
              type="search"
              unselectable="on"
              value=""
            />
          </span>
          <span
            class="ant-select-selection-item"
            title="Between"
          >
            Between
          </span>
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
      >
        <div>
          <div
            id="rc_select_TEST_OR_SSR_list"
            role="listbox"
            style="height: 0px; width: 0px; overflow: hidden;"
          >
            <div
              aria-label="Between"
              aria-selected="true"
              id="rc_select_TEST_OR_SSR_list_0"
              role="option"
            >
              1
            </div>
            <div
              aria-label="Except"
              aria-selected="false"
              id="rc_select_TEST_OR_SSR_list_1"
              role="option"
            >
              2
            </div>
          </div>
          <div
            class="rc-virtual-list"
            style="position: relative;"
          >
            <div
              class="rc-virtual-list-holder"
              style="max-height: 256px; overflow-y: auto;"
            >
              <div>
                <div
                  class="rc-virtual-list-holder-inner"
                  style="display: flex; flex-direction: column;"
                >
                  <div
                    aria-selected="true"
                    class="ant-select-item ant-select-item-option ant-select-item-option-active ant-select-item-option-selected"
                    title="Between"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Between
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                  <div
                    aria-selected="false"
                    class="ant-select-item ant-select-item-option"
                    title="Except"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Except
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select: none;"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <input
      class="ant-input ant-input-outlined"
      placeholder="Minimum"
      style="width: 100px; text-align: center;"
      type="text"
      value=""
    />
    <input
      class="ant-input ant-input-disabled ant-input-outlined site-input-split"
      disabled=""
      placeholder="~"
      style="width: 30px; border-inline-start: 0; border-inline-end: 0; pointer-events: none;"
      type="text"
      value=""
    />
    <input
      class="ant-input ant-input-outlined site-input-right"
      placeholder="Maximum"
      style="width: 100px; text-align: center;"
      type="text"
      value=""
    />
  </span>
  <br />
  <span
    class="ant-input-group ant-input-group-compact"
  >
    <div
      class="ant-select ant-select-outlined ant-select-single ant-select-show-arrow"
      style="width: 30%;"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="rc_select_TEST_OR_SSR_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="rc_select_TEST_OR_SSR_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="rc_select_TEST_OR_SSR"
              readonly=""
              role="combobox"
              style="opacity: 0;"
              type="search"
              unselectable="on"
              value=""
            />
          </span>
          <span
            class="ant-select-selection-item"
            title="Sign Up"
          >
            Sign Up
          </span>
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
      >
        <div>
          <div
            id="rc_select_TEST_OR_SSR_list"
            role="listbox"
            style="height: 0px; width: 0px; overflow: hidden;"
          >
            <div
              aria-label="Sign Up"
              aria-selected="true"
              id="rc_select_TEST_OR_SSR_list_0"
              role="option"
            >
              Sign Up
            </div>
            <div
              aria-label="Sign In"
              aria-selected="false"
              id="rc_select_TEST_OR_SSR_list_1"
              role="option"
            >
              Sign In
            </div>
          </div>
          <div
            class="rc-virtual-list"
            style="position: relative;"
          >
            <div
              class="rc-virtual-list-holder"
              style="max-height: 256px; overflow-y: auto;"
            >
              <div>
                <div
                  class="rc-virtual-list-holder-inner"
                  style="display: flex; flex-direction: column;"
                >
                  <div
                    aria-selected="true"
                    class="ant-select-item ant-select-item-option ant-select-item-option-active ant-select-item-option-selected"
                    title="Sign Up"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Sign Up
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                  <div
                    aria-selected="false"
                    class="ant-select-item ant-select-item-option"
                    title="Sign In"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Sign In
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select: none;"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-select ant-select-outlined ant-select-auto-complete ant-select-single ant-select-show-search"
      style="width: 70%;"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="rc_select_TEST_OR_SSR_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="rc_select_TEST_OR_SSR_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="rc_select_TEST_OR_SSR"
              role="combobox"
              type="search"
              value=""
            />
          </span>
          <span
            class="ant-select-selection-placeholder"
          >
            Email
          </span>
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
      >
        <div>
          <div
            id="rc_select_TEST_OR_SSR_list"
            role="listbox"
            style="height: 0px; width: 0px; overflow: hidden;"
          >
            <div
              aria-selected="false"
              id="rc_select_TEST_OR_SSR_list_0"
              role="option"
            >
              text 1
            </div>
          </div>
          <div
            class="rc-virtual-list"
            style="position: relative;"
          >
            <div
              class="rc-virtual-list-holder"
              style="max-height: 256px; overflow-y: auto;"
            >
              <div>
                <div
                  class="rc-virtual-list-holder-inner"
                  style="display: flex; flex-direction: column;"
                >
                  <div
                    aria-selected="false"
                    class="ant-select-item ant-select-item-option"
                    title="text 1"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      text 1
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                  <div
                    aria-selected="false"
                    class="ant-select-item ant-select-item-option"
                    title="text 2"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      text 2
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </span>
  <br />
  <span
    class="ant-input-group ant-input-group-compact"
  >
    <div
      class="ant-select ant-select-outlined ant-select-single ant-select-show-arrow"
      style="width: 30%;"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="rc_select_TEST_OR_SSR_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="rc_select_TEST_OR_SSR_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="rc_select_TEST_OR_SSR"
              readonly=""
              role="combobox"
              style="opacity: 0;"
              type="search"
              unselectable="on"
              value=""
            />
          </span>
          <span
            class="ant-select-selection-item"
            title="Home"
          >
            Home
          </span>
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
      >
        <div>
          <div
            id="rc_select_TEST_OR_SSR_list"
            role="listbox"
            style="height: 0px; width: 0px; overflow: hidden;"
          >
            <div
              aria-label="Home"
              aria-selected="true"
              id="rc_select_TEST_OR_SSR_list_0"
              role="option"
            >
              Home
            </div>
            <div
              aria-label="Company"
              aria-selected="false"
              id="rc_select_TEST_OR_SSR_list_1"
              role="option"
            >
              Company
            </div>
          </div>
          <div
            class="rc-virtual-list"
            style="position: relative;"
          >
            <div
              class="rc-virtual-list-holder"
              style="max-height: 256px; overflow-y: auto;"
            >
              <div>
                <div
                  class="rc-virtual-list-holder-inner"
                  style="display: flex; flex-direction: column;"
                >
                  <div
                    aria-selected="true"
                    class="ant-select-item ant-select-item-option ant-select-item-option-active ant-select-item-option-selected"
                    title="Home"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Home
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                  <div
                    aria-selected="false"
                    class="ant-select-item ant-select-item-option"
                    title="Company"
                  >
                    <div
                      class="ant-select-item-option-content"
                    >
                      Company
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-item-option-state"
                      style="user-select: none;"
                      unselectable="on"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select: none;"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
    <div
      class="ant-select ant-cascader ant-select-outlined ant-select-single ant-select-allow-clear ant-select-show-arrow"
      style="width: 70%;"
    >
      <div
        class="ant-select-selector"
      >
        <span
          class="ant-select-selection-wrap"
        >
          <span
            class="ant-select-selection-search"
          >
            <input
              aria-autocomplete="list"
              aria-controls="rc_select_TEST_OR_SSR_list"
              aria-expanded="false"
              aria-haspopup="listbox"
              aria-owns="rc_select_TEST_OR_SSR_list"
              autocomplete="off"
              class="ant-select-selection-search-input"
              id="rc_select_TEST_OR_SSR"
              readonly=""
              role="combobox"
              style="opacity: 0;"
              type="search"
              unselectable="on"
              value=""
            />
          </span>
          <span
            class="ant-select-selection-placeholder"
          >
            Select Address
          </span>
        </span>
      </div>
      <div
        class="ant-select-dropdown ant-slide-up-appear ant-slide-up-appear-prepare ant-slide-up ant-cascader-dropdown ant-select-dropdown-placement-bottomLeft"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box; min-width: auto;"
      >
        <div>
          <div
            class="ant-cascader-menus"
          >
            <ul
              class="ant-cascader-menu"
              role="menu"
            >
              <li
                aria-checked="false"
                class="ant-cascader-menu-item ant-cascader-menu-item-expand"
                data-path-key="zhejiang"
                role="menuitemcheckbox"
                title="Zhejiang"
              >
                <div
                  class="ant-cascader-menu-item-content"
                >
                  Zhejiang
                </div>
                <div
                  class="ant-cascader-menu-item-expand-icon"
                >
                  <span
                    aria-label="right"
                    class="anticon anticon-right"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="right"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                      />
                    </svg>
                  </span>
                </div>
              </li>
              <li
                aria-checked="false"
                class="ant-cascader-menu-item ant-cascader-menu-item-expand"
                data-path-key="jiangsu"
                role="menuitemcheckbox"
                title="Jiangsu"
              >
                <div
                  class="ant-cascader-menu-item-content"
                >
                  Jiangsu
                </div>
                <div
                  class="ant-cascader-menu-item-expand-icon"
                >
                  <span
                    aria-label="right"
                    class="anticon anticon-right"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="right"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                      />
                    </svg>
                  </span>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <span
        aria-hidden="true"
        class="ant-select-arrow"
        style="user-select: none;"
        unselectable="on"
      >
        <span
          aria-label="down"
          class="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
  </span>
</div>
`;

exports[`renders components/input/demo/group.tsx extend context correctly 2`] = `
[
  "Warning: [antd: Input.Group] \`Input.Group\` is deprecated. Please use \`Space.Compact\` instead.",
]
`;

exports[`renders components/input/demo/otp.tsx extend context correctly 1`] = `
<div
  class="ant-flex ant-flex-align-flex-start ant-flex-gap-middle ant-flex-vertical"
>
  <h5
    class="ant-typography"
  >
    With formatter (Upcase)
  </h5>
  <div
    class="ant-otp"
    role="group"
  >
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 1"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 2"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 3"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 4"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 5"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 6"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
  </div>
  <h5
    class="ant-typography"
  >
    With Disabled
  </h5>
  <div
    class="ant-otp"
    role="group"
  >
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 1"
        class="ant-input ant-input-disabled ant-input-outlined ant-otp-input"
        disabled=""
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 2"
        class="ant-input ant-input-disabled ant-input-outlined ant-otp-input"
        disabled=""
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 3"
        class="ant-input ant-input-disabled ant-input-outlined ant-otp-input"
        disabled=""
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 4"
        class="ant-input ant-input-disabled ant-input-outlined ant-otp-input"
        disabled=""
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 5"
        class="ant-input ant-input-disabled ant-input-outlined ant-otp-input"
        disabled=""
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 6"
        class="ant-input ant-input-disabled ant-input-outlined ant-otp-input"
        disabled=""
        size="1"
        type="text"
        value=""
      />
    </span>
  </div>
  <h5
    class="ant-typography"
  >
    With Length (8)
  </h5>
  <div
    class="ant-otp"
    role="group"
  >
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 1"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 2"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 3"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 4"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 5"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 6"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 7"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 8"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
  </div>
  <h5
    class="ant-typography"
  >
    With variant
  </h5>
  <div
    class="ant-otp"
    role="group"
  >
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 1"
        class="ant-input ant-input-filled ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 2"
        class="ant-input ant-input-filled ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 3"
        class="ant-input ant-input-filled ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 4"
        class="ant-input ant-input-filled ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 5"
        class="ant-input ant-input-filled ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 6"
        class="ant-input ant-input-filled ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
  </div>
  <h5
    class="ant-typography"
  >
    With custom display character
  </h5>
  <div
    class="ant-otp"
    role="group"
  >
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 1"
        class="ant-input ant-input-outlined ant-otp-input ant-otp-mask-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 2"
        class="ant-input ant-input-outlined ant-otp-input ant-otp-mask-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 3"
        class="ant-input ant-input-outlined ant-otp-input ant-otp-mask-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 4"
        class="ant-input ant-input-outlined ant-otp-input ant-otp-mask-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 5"
        class="ant-input ant-input-outlined ant-otp-input ant-otp-mask-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 6"
        class="ant-input ant-input-outlined ant-otp-input ant-otp-mask-input"
        size="1"
        type="text"
        value=""
      />
    </span>
  </div>
  <h5
    class="ant-typography"
  >
    With custom ReactNode separator
  </h5>
  <div
    class="ant-otp"
    role="group"
  >
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 1"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-separator"
    >
      <span>
        /
      </span>
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 2"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-separator"
    >
      <span>
        /
      </span>
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 3"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-separator"
    >
      <span>
        /
      </span>
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 4"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-separator"
    >
      <span>
        /
      </span>
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 5"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-separator"
    >
      <span>
        /
      </span>
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 6"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
  </div>
  <h5
    class="ant-typography"
  >
    With custom function separator
  </h5>
  <div
    class="ant-otp"
    role="group"
  >
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 1"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-separator"
    >
      <span
        style="color: blue;"
      >
        —
      </span>
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 2"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-separator"
    >
      <span
        style="color: red;"
      >
        —
      </span>
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 3"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-separator"
    >
      <span
        style="color: blue;"
      >
        —
      </span>
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 4"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-separator"
    >
      <span
        style="color: red;"
      >
        —
      </span>
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 5"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
    <span
      class="ant-otp-separator"
    >
      <span
        style="color: blue;"
      >
        —
      </span>
    </span>
    <span
      class="ant-otp-input-wrapper"
      role="presentation"
    >
      <input
        aria-label="OTP Input 6"
        class="ant-input ant-input-outlined ant-otp-input"
        size="1"
        type="text"
        value=""
      />
    </span>
  </div>
</div>
`;

exports[`renders components/input/demo/otp.tsx extend context correctly 2`] = `
[
  "Warning: [antd: Input.OTP] \`mask\` prop should be a single character.",
]
`;

exports[`renders components/input/demo/password-input.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small"
>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-affix-wrapper ant-input-outlined ant-input-password"
    >
      <input
        class="ant-input"
        placeholder="input password"
        type="password"
        value=""
      />
      <span
        class="ant-input-suffix"
      >
        <span
          aria-label="eye-invisible"
          class="anticon anticon-eye-invisible ant-input-password-icon"
          role="img"
          tabindex="-1"
        >
          <svg
            aria-hidden="true"
            data-icon="eye-invisible"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"
            />
            <path
              d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"
            />
          </svg>
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-affix-wrapper ant-input-outlined ant-input-password"
    >
      <input
        class="ant-input"
        placeholder="input password"
        type="password"
        value=""
      />
      <span
        class="ant-input-suffix"
      >
        <span
          aria-label="eye-invisible"
          class="anticon anticon-eye-invisible ant-input-password-icon"
          role="img"
          tabindex="-1"
        >
          <svg
            aria-hidden="true"
            data-icon="eye-invisible"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"
            />
            <path
              d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"
            />
          </svg>
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-space-item"
  >
    <div
      class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small"
    >
      <div
        class="ant-space-item"
      >
        <span
          class="ant-input-affix-wrapper ant-input-outlined ant-input-password"
        >
          <input
            class="ant-input"
            placeholder="input password"
            type="password"
            value=""
          />
          <span
            class="ant-input-suffix"
          >
            <span
              aria-label="eye-invisible"
              class="anticon anticon-eye-invisible ant-input-password-icon"
              role="img"
              tabindex="-1"
            >
              <svg
                aria-hidden="true"
                data-icon="eye-invisible"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"
                />
                <path
                  d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"
                />
              </svg>
            </span>
          </span>
        </span>
      </div>
      <div
        class="ant-space-item"
      >
        <button
          class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
          style="width: 80px;"
          type="button"
        >
          <span>
            Show
          </span>
        </button>
      </div>
    </div>
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-affix-wrapper ant-input-disabled ant-input-affix-wrapper-disabled ant-input-outlined ant-input-password"
    >
      <input
        class="ant-input ant-input-disabled"
        disabled=""
        placeholder="disabled input password"
        type="password"
        value=""
      />
      <span
        class="ant-input-suffix"
      >
        <span
          aria-label="eye-invisible"
          class="anticon anticon-eye-invisible ant-input-password-icon"
          role="img"
          tabindex="-1"
        >
          <svg
            aria-hidden="true"
            data-icon="eye-invisible"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"
            />
            <path
              d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"
            />
          </svg>
        </span>
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/input/demo/password-input.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/presuffix.tsx extend context correctly 1`] = `
Array [
  <span
    class="ant-input-affix-wrapper ant-input-outlined"
  >
    <span
      class="ant-input-prefix"
    >
      <span
        aria-label="user"
        class="anticon anticon-user"
        role="img"
        style="color: rgba(0, 0, 0, 0.25);"
      >
        <svg
          aria-hidden="true"
          data-icon="user"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
          />
        </svg>
      </span>
    </span>
    <input
      class="ant-input"
      placeholder="Enter your username"
      type="text"
      value=""
    />
    <span
      class="ant-input-suffix"
    >
      <span
        aria-describedby="test-id"
        aria-label="info-circle"
        class="anticon anticon-info-circle"
        role="img"
        style="color: rgba(0, 0, 0, 0.45);"
      >
        <svg
          aria-hidden="true"
          data-icon="info-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
          />
          <path
            d="M464 336a48 48 0 1096 0 48 48 0 10-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z"
          />
        </svg>
      </span>
      <div
        class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast ant-tooltip-placement-top"
        style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
      >
        <div
          class="ant-tooltip-arrow"
          style="position: absolute; bottom: 0px; left: 0px;"
        />
        <div
          class="ant-tooltip-content"
        >
          <div
            class="ant-tooltip-inner"
            id="test-id"
            role="tooltip"
          >
            Extra information
          </div>
        </div>
      </div>
    </span>
  </span>,
  <br />,
  <br />,
  <span
    class="ant-input-affix-wrapper ant-input-outlined"
  >
    <span
      class="ant-input-prefix"
    >
      ￥
    </span>
    <input
      class="ant-input"
      type="text"
      value=""
    />
    <span
      class="ant-input-suffix"
    >
      RMB
    </span>
  </span>,
  <br />,
  <br />,
  <span
    class="ant-input-affix-wrapper ant-input-disabled ant-input-affix-wrapper-disabled ant-input-outlined"
  >
    <span
      class="ant-input-prefix"
    >
      ￥
    </span>
    <input
      class="ant-input ant-input-disabled"
      disabled=""
      type="text"
      value=""
    />
    <span
      class="ant-input-suffix"
    >
      RMB
    </span>
  </span>,
]
`;

exports[`renders components/input/demo/presuffix.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/search-input.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small"
>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search"
      style="width: 200px;"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <input
          class="ant-input ant-input-outlined"
          placeholder="input search text"
          type="search"
          value=""
        />
        <span
          class="ant-input-group-addon"
        >
          <button
            class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-input-search-button"
            type="button"
          >
            <span
              class="ant-btn-icon"
            >
              <span
                aria-label="search"
                class="anticon anticon-search"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="search"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                  />
                </svg>
              </span>
            </span>
          </button>
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search"
      style="width: 200px;"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <span
          class="ant-input-affix-wrapper ant-input-outlined"
        >
          <input
            class="ant-input"
            placeholder="input search text"
            type="search"
            value=""
          />
          <span
            class="ant-input-suffix"
          >
            <button
              class="ant-input-clear-icon ant-input-clear-icon-hidden"
              tabindex="-1"
              type="button"
            >
              <span
                aria-label="close-circle"
                class="anticon anticon-close-circle"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="close-circle"
                  fill="currentColor"
                  fill-rule="evenodd"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                  />
                </svg>
              </span>
            </button>
          </span>
        </span>
        <span
          class="ant-input-group-addon"
        >
          <button
            class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-input-search-button"
            type="button"
          >
            <span
              class="ant-btn-icon"
            >
              <span
                aria-label="search"
                class="anticon anticon-search"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="search"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                  />
                </svg>
              </span>
            </span>
          </button>
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search"
      style="width: 304px;"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <span
          class="ant-input-group-addon"
        >
          https://
        </span>
        <span
          class="ant-input-affix-wrapper ant-input-outlined"
        >
          <input
            class="ant-input"
            placeholder="input search text"
            type="search"
            value=""
          />
          <span
            class="ant-input-suffix"
          >
            <button
              class="ant-input-clear-icon ant-input-clear-icon-hidden"
              tabindex="-1"
              type="button"
            >
              <span
                aria-label="close-circle"
                class="anticon anticon-close-circle"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="close-circle"
                  fill="currentColor"
                  fill-rule="evenodd"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                  />
                </svg>
              </span>
            </button>
          </span>
        </span>
        <span
          class="ant-input-group-addon"
        >
          <button
            class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-input-search-button"
            type="button"
          >
            <span
              class="ant-btn-icon"
            >
              <span
                aria-label="search"
                class="anticon anticon-search"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="search"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                  />
                </svg>
              </span>
            </span>
          </button>
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search ant-input-search-with-button"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <input
          class="ant-input ant-input-outlined"
          placeholder="input search text"
          type="search"
          value=""
        />
        <span
          class="ant-input-group-addon"
        >
          <button
            class="ant-btn ant-btn-default ant-btn-color-primary ant-btn-variant-solid ant-input-search-button"
            type="button"
          >
            <span
              class="ant-btn-icon"
            >
              <span
                aria-label="search"
                class="anticon anticon-search"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="search"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                  />
                </svg>
              </span>
            </span>
          </button>
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-lg ant-input-group-wrapper-outlined ant-input-search ant-input-search-large ant-input-search-with-button"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <span
          class="ant-input-affix-wrapper ant-input-affix-wrapper-lg ant-input-outlined"
        >
          <input
            class="ant-input ant-input-lg"
            placeholder="input search text"
            type="search"
            value=""
          />
          <span
            class="ant-input-suffix"
          >
            <button
              class="ant-input-clear-icon ant-input-clear-icon-hidden"
              tabindex="-1"
              type="button"
            >
              <span
                aria-label="close-circle"
                class="anticon anticon-close-circle"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="close-circle"
                  fill="currentColor"
                  fill-rule="evenodd"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                  />
                </svg>
              </span>
            </button>
          </span>
        </span>
        <span
          class="ant-input-group-addon"
        >
          <button
            class="ant-btn ant-btn-default ant-btn-color-primary ant-btn-variant-solid ant-btn-lg ant-input-search-button"
            type="button"
          >
            <span>
              Search
            </span>
          </button>
        </span>
      </span>
    </span>
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-group-wrapper ant-input-group-wrapper-lg ant-input-group-wrapper-outlined ant-input-search ant-input-search-large ant-input-search-with-button"
    >
      <span
        class="ant-input-wrapper ant-input-group"
      >
        <span
          class="ant-input-affix-wrapper ant-input-affix-wrapper-lg ant-input-outlined"
        >
          <input
            class="ant-input ant-input-lg"
            placeholder="input search text"
            type="search"
            value=""
          />
          <span
            class="ant-input-suffix"
          >
            <span
              aria-label="audio"
              class="anticon anticon-audio"
              role="img"
              style="font-size: 16px; color: rgb(22, 119, 255);"
            >
              <svg
                aria-hidden="true"
                data-icon="audio"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M842 454c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 140.3-113.7 254-254 254S258 594.3 258 454c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8 0 168.7 126.6 307.9 290 327.6V884H326.7c-13.7 0-24.7 14.3-24.7 32v36c0 4.4 2.8 8 6.2 8h407.6c3.4 0 6.2-3.6 6.2-8v-36c0-17.7-11-32-24.7-32H548V782.1c165.3-18 294-158 294-328.1zM512 624c93.9 0 170-75.2 170-168V232c0-92.8-76.1-168-170-168s-170 75.2-170 168v224c0 92.8 76.1 168 170 168zm-94-392c0-50.6 41.9-92 94-92s94 41.4 94 92v224c0 50.6-41.9 92-94 92s-94-41.4-94-92V232z"
                />
              </svg>
            </span>
          </span>
        </span>
        <span
          class="ant-input-group-addon"
        >
          <button
            class="ant-btn ant-btn-default ant-btn-color-primary ant-btn-variant-solid ant-btn-lg ant-input-search-button"
            type="button"
          >
            <span>
              Search
            </span>
          </button>
        </span>
      </span>
    </span>
  </div>
</div>
`;

exports[`renders components/input/demo/search-input.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/search-input-loading.tsx extend context correctly 1`] = `
Array [
  <span
    class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search"
  >
    <span
      class="ant-input-wrapper ant-input-group"
    >
      <input
        class="ant-input ant-input-outlined"
        placeholder="input search loading default"
        type="search"
        value=""
      />
      <span
        class="ant-input-group-addon"
      >
        <button
          class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined ant-btn-icon-only ant-btn-loading ant-input-search-button"
          type="button"
        >
          <span
            class="ant-btn-icon ant-btn-loading-icon"
          >
            <span
              aria-label="loading"
              class="anticon anticon-loading anticon-spin"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="loading"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="0 0 1024 1024"
                width="1em"
              >
                <path
                  d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
                />
              </svg>
            </span>
          </span>
        </button>
      </span>
    </span>
  </span>,
  <br />,
  <br />,
  <span
    class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-search ant-input-search-with-button"
  >
    <span
      class="ant-input-wrapper ant-input-group"
    >
      <input
        class="ant-input ant-input-outlined"
        placeholder="input search loading with enterButton"
        type="search"
        value=""
      />
      <span
        class="ant-input-group-addon"
      >
        <button
          class="ant-btn ant-btn-default ant-btn-color-primary ant-btn-variant-solid ant-btn-loading ant-input-search-button"
          type="button"
        >
          <span
            class="ant-btn-icon ant-btn-loading-icon"
          >
            <span
              aria-label="loading"
              class="anticon anticon-loading anticon-spin"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="loading"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="0 0 1024 1024"
                width="1em"
              >
                <path
                  d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
                />
              </svg>
            </span>
          </span>
        </button>
      </span>
    </span>
  </span>,
  <br />,
  <br />,
  <span
    class="ant-input-group-wrapper ant-input-group-wrapper-lg ant-input-group-wrapper-outlined ant-input-search ant-input-search-large ant-input-search-with-button"
  >
    <span
      class="ant-input-wrapper ant-input-group"
    >
      <input
        class="ant-input ant-input-lg ant-input-outlined"
        placeholder="input search text"
        type="search"
        value=""
      />
      <span
        class="ant-input-group-addon"
      >
        <button
          class="ant-btn ant-btn-default ant-btn-color-primary ant-btn-variant-solid ant-btn-lg ant-btn-loading ant-input-search-button"
          type="button"
        >
          <span
            class="ant-btn-icon ant-btn-loading-icon"
          >
            <span
              aria-label="loading"
              class="anticon anticon-loading anticon-spin"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="loading"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="0 0 1024 1024"
                width="1em"
              >
                <path
                  d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
                />
              </svg>
            </span>
          </span>
          <span>
            Search
          </span>
        </button>
      </span>
    </span>
  </span>,
]
`;

exports[`renders components/input/demo/search-input-loading.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/show-count.tsx extend context correctly 1`] = `
<div
  class="ant-flex ant-flex-align-stretch ant-flex-vertical"
  style="gap: 32px;"
>
  <span
    class="ant-input-affix-wrapper ant-input-outlined"
  >
    <input
      class="ant-input"
      maxlength="20"
      type="text"
      value=""
    />
    <span
      class="ant-input-suffix"
    >
      <span
        class="ant-input-show-count-suffix"
      >
        0 / 20
      </span>
    </span>
  </span>
  <span
    class="ant-input-affix-wrapper ant-input-textarea-affix-wrapper ant-input-textarea-show-count ant-input-show-count ant-input-outlined"
    data-count="0 / 100"
  >
    <textarea
      class="ant-input"
      maxlength="100"
      placeholder="can resize"
    />
    <span
      class="ant-input-suffix"
    >
      <span
        class="ant-input-data-count"
      >
        0 / 100
      </span>
    </span>
  </span>
  <span
    class="ant-input-affix-wrapper ant-input-textarea-affix-wrapper ant-input-textarea-show-count ant-input-show-count ant-input-outlined"
    data-count="0 / 100"
    style="height: 120px; resize: none;"
  >
    <textarea
      class="ant-input"
      maxlength="100"
      placeholder="disable resize"
      style="resize: none;"
    />
    <span
      class="ant-input-suffix"
    >
      <span
        class="ant-input-data-count"
      >
        0 / 100
      </span>
    </span>
  </span>
</div>
`;

exports[`renders components/input/demo/show-count.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/size.tsx extend context correctly 1`] = `
Array [
  <span
    class="ant-input-affix-wrapper ant-input-affix-wrapper-lg ant-input-outlined"
  >
    <span
      class="ant-input-prefix"
    >
      <span
        aria-label="user"
        class="anticon anticon-user"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="user"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
          />
        </svg>
      </span>
    </span>
    <input
      class="ant-input ant-input-lg"
      placeholder="large size"
      type="text"
      value=""
    />
  </span>,
  <br />,
  <br />,
  <span
    class="ant-input-affix-wrapper ant-input-outlined"
  >
    <span
      class="ant-input-prefix"
    >
      <span
        aria-label="user"
        class="anticon anticon-user"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="user"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
          />
        </svg>
      </span>
    </span>
    <input
      class="ant-input"
      placeholder="default size"
      type="text"
      value=""
    />
  </span>,
  <br />,
  <br />,
  <span
    class="ant-input-affix-wrapper ant-input-affix-wrapper-sm ant-input-outlined"
  >
    <span
      class="ant-input-prefix"
    >
      <span
        aria-label="user"
        class="anticon anticon-user"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="user"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
          />
        </svg>
      </span>
    </span>
    <input
      class="ant-input ant-input-sm"
      placeholder="small size"
      type="text"
      value=""
    />
  </span>,
]
`;

exports[`renders components/input/demo/size.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/status.tsx extend context correctly 1`] = `
<div
  class="ant-space ant-space-vertical ant-space-gap-row-small ant-space-gap-col-small"
  style="width: 100%;"
>
  <div
    class="ant-space-item"
  >
    <input
      class="ant-input ant-input-outlined ant-input-status-error"
      placeholder="Error"
      type="text"
      value=""
    />
  </div>
  <div
    class="ant-space-item"
  >
    <input
      class="ant-input ant-input-outlined ant-input-status-warning"
      placeholder="Warning"
      type="text"
      value=""
    />
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-affix-wrapper ant-input-outlined ant-input-status-error"
    >
      <span
        class="ant-input-prefix"
      >
        <span
          aria-label="clock-circle"
          class="anticon anticon-clock-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="clock-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
            />
          </svg>
        </span>
      </span>
      <input
        class="ant-input"
        placeholder="Error with prefix"
        type="text"
        value=""
      />
    </span>
  </div>
  <div
    class="ant-space-item"
  >
    <span
      class="ant-input-affix-wrapper ant-input-outlined ant-input-status-warning"
    >
      <span
        class="ant-input-prefix"
      >
        <span
          aria-label="clock-circle"
          class="anticon anticon-clock-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="clock-circle"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
            />
            <path
              d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
            />
          </svg>
        </span>
      </span>
      <input
        class="ant-input"
        placeholder="Warning with prefix"
        type="text"
        value=""
      />
    </span>
  </div>
</div>
`;

exports[`renders components/input/demo/status.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/textarea.tsx extend context correctly 1`] = `
Array [
  <textarea
    class="ant-input ant-input-outlined"
    rows="4"
  />,
  <br />,
  <br />,
  <textarea
    class="ant-input ant-input-outlined"
    maxlength="6"
    placeholder="maxLength is 6"
    rows="4"
  />,
]
`;

exports[`renders components/input/demo/textarea.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/textarea-resize.tsx extend context correctly 1`] = `
Array [
  <button
    class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
    style="margin-bottom: 16px;"
    type="button"
  >
    <span>
      Auto Resize: false
    </span>
  </button>,
  <textarea
    class="ant-input ant-input-outlined"
    rows="4"
  >
    The autoSize property applies to textarea nodes, and only the height changes automatically. In addition, autoSize can be set to an object, specifying the minimum number of rows and the maximum number of rows. The autoSize property applies to textarea nodes, and only the height changes automatically. In addition, autoSize can be set to an object, specifying the minimum number of rows and the maximum number of rows.
  </textarea>,
  <span
    class="ant-input-affix-wrapper ant-input-textarea-affix-wrapper ant-input-textarea-allow-clear ant-input-outlined"
    style="width: 93px;"
  >
    <textarea
      class="ant-input"
    />
    <span
      class="ant-input-suffix"
    >
      <button
        class="ant-input-clear-icon ant-input-clear-icon-hidden"
        tabindex="-1"
        type="button"
      >
        <span
          aria-label="close-circle"
          class="anticon anticon-close-circle"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close-circle"
            fill="currentColor"
            fill-rule="evenodd"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
            />
          </svg>
        </span>
      </button>
    </span>
  </span>,
  <br />,
  <span
    class="ant-input-affix-wrapper ant-input-textarea-affix-wrapper ant-input-textarea-show-count ant-input-show-count ant-input-outlined"
    data-count="0"
    style="resize: both;"
  >
    <textarea
      class="ant-input"
      style="resize: both;"
    />
    <span
      class="ant-input-suffix"
    >
      <span
        class="ant-input-data-count"
      >
        0
      </span>
    </span>
  </span>,
]
`;

exports[`renders components/input/demo/textarea-resize.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/tooltip.tsx extend context correctly 1`] = `
Array [
  <input
    aria-describedby="test-id"
    class="ant-input ant-input-outlined"
    maxlength="16"
    placeholder="Input a number"
    style="width: 120px;"
    type="text"
    value=""
  />,
  <div
    class="ant-tooltip ant-zoom-big-fast-appear ant-zoom-big-fast-appear-prepare ant-zoom-big-fast numeric-input ant-tooltip-placement-topLeft"
    style="--arrow-x: 0px; --arrow-y: 0px; left: -1000vw; top: -1000vh; box-sizing: border-box;"
  >
    <div
      class="ant-tooltip-arrow"
      style="position: absolute;"
    />
    <div
      class="ant-tooltip-content"
    >
      <div
        class="ant-tooltip-inner"
        id="test-id"
        role="tooltip"
      >
        Input a number
      </div>
    </div>
  </div>,
]
`;

exports[`renders components/input/demo/tooltip.tsx extend context correctly 2`] = `[]`;

exports[`renders components/input/demo/variant.tsx extend context correctly 1`] = `
<div
  class="ant-flex ant-flex-align-stretch ant-flex-vertical"
  style="gap: 12px;"
>
  <input
    class="ant-input ant-input-outlined"
    placeholder="Outlined"
    type="text"
    value=""
  />
  <input
    class="ant-input ant-input-filled"
    placeholder="Filled"
    type="text"
    value=""
  />
  <input
    class="ant-input ant-input-borderless"
    placeholder="Borderless"
    type="text"
    value=""
  />
  <input
    class="ant-input ant-input-underlined"
    placeholder="Underlined"
    type="text"
    value=""
  />
  <span
    class="ant-input-group-wrapper ant-input-group-wrapper-filled ant-input-search"
  >
    <span
      class="ant-input-wrapper ant-input-group"
    >
      <input
        class="ant-input ant-input-filled"
        placeholder="Filled"
        type="search"
        value=""
      />
      <span
        class="ant-input-group-addon"
      >
        <button
          class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-text ant-btn-icon-only ant-input-search-button"
          type="button"
        >
          <span
            class="ant-btn-icon"
          >
            <span
              aria-label="search"
              class="anticon anticon-search"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="search"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M909.6 854.5L649.9 594.8C690.2 542.7 712 479 712 412c0-80.2-31.3-155.4-87.9-212.1-56.6-56.7-132-87.9-212.1-87.9s-155.5 31.3-212.1 87.9C143.2 256.5 112 331.8 112 412c0 80.1 31.3 155.5 87.9 212.1C256.5 680.8 331.8 712 412 712c67 0 130.6-21.8 182.7-62l259.7 259.6a8.2 8.2 0 0011.6 0l43.6-43.5a8.2 8.2 0 000-11.6zM570.4 570.4C528 612.7 471.8 636 412 636s-116-23.3-158.4-65.6C211.3 528 188 471.8 188 412s23.3-116.1 65.6-158.4C296 211.3 352.2 188 412 188s116.1 23.2 158.4 65.6S636 352.2 636 412s-23.3 116.1-65.6 158.4z"
                />
              </svg>
            </span>
          </span>
        </button>
      </span>
    </span>
  </span>
</div>
`;

exports[`renders components/input/demo/variant.tsx extend context correctly 2`] = `[]`;
