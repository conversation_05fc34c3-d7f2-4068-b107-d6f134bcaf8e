## Component Definition

The essence of Breadcrumb is to understand the location of the current page and enable upward navigation.

<code src="./design/behavior-pattern.tsx" inline></code>

## Basic Usage

<code src="./demo/basic" description="Used when users need to understand the position of the current page in the system hierarchy or need to navigate upward. This is the most basic usage.">Determine Location and Navigate Upward</code>

## Interactive Variants

<code src="./demo/overlay" description="With dropdown menu, the content in the dropdown can carry content at the same level as the first-level breadcrumb, or can carry sub-level content of the breadcrumb, facilitating quick switching.">Quick Navigation</code>

## Style Variants

<code src="./demo/withIcon" description="Icons replace part of the text, or add icons before the text.">Icon Style</code>

<code src="./demo/separator" description="Separators can use the greater-than symbol from mathematics.">Custom Separator Style</code>
