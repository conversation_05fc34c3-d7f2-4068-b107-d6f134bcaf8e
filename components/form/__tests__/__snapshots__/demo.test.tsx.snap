// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`renders components/form/demo/advanced-search.tsx correctly 1`] = `
Array [
  <form
    class="ant-form ant-form-horizontal"
    id="advanced_search"
    style="max-width:none;background:rgba(0,0,0,0.02);border-radius:8px;padding:24px"
  >
    <div
      class="ant-row"
      style="margin-left:-12px;margin-right:-12px"
    >
      <div
        class="ant-col ant-col-8"
        style="padding-left:12px;padding-right:12px"
      >
        <div
          class="ant-form-item"
        >
          <div
            class="ant-row ant-form-item-row"
          >
            <div
              class="ant-col ant-form-item-label"
            >
              <label
                class="ant-form-item-required"
                for="advanced_search_field-0"
                title="Field 0"
              >
                Field 0
              </label>
            </div>
            <div
              class="ant-col ant-form-item-control"
            >
              <div
                class="ant-form-item-control-input"
              >
                <div
                  class="ant-form-item-control-input-content"
                >
                  <input
                    aria-required="true"
                    class="ant-input ant-input-outlined"
                    id="advanced_search_field-0"
                    placeholder="placeholder"
                    type="text"
                    value=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-col ant-col-8"
        style="padding-left:12px;padding-right:12px"
      >
        <div
          class="ant-form-item"
        >
          <div
            class="ant-row ant-form-item-row"
          >
            <div
              class="ant-col ant-form-item-label"
            >
              <label
                class="ant-form-item-required"
                for="advanced_search_field-1"
                title="Field 1"
              >
                Field 1
              </label>
            </div>
            <div
              class="ant-col ant-form-item-control"
            >
              <div
                class="ant-form-item-control-input"
              >
                <div
                  class="ant-form-item-control-input-content"
                >
                  <div
                    aria-required="true"
                    class="ant-select ant-select-outlined ant-select-in-form-item ant-select-single ant-select-show-arrow"
                  >
                    <div
                      class="ant-select-selector"
                    >
                      <span
                        class="ant-select-selection-wrap"
                      >
                        <span
                          class="ant-select-selection-search"
                        >
                          <input
                            aria-autocomplete="list"
                            aria-controls="advanced_search_field-1_list"
                            aria-expanded="false"
                            aria-haspopup="listbox"
                            aria-owns="advanced_search_field-1_list"
                            aria-required="true"
                            autocomplete="off"
                            class="ant-select-selection-search-input"
                            id="advanced_search_field-1"
                            readonly=""
                            role="combobox"
                            style="opacity:0"
                            type="search"
                            unselectable="on"
                            value=""
                          />
                        </span>
                        <span
                          class="ant-select-selection-item"
                          title="longlonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglong"
                        >
                          longlonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglong
                        </span>
                      </span>
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-arrow"
                      style="user-select:none;-webkit-user-select:none"
                      unselectable="on"
                    >
                      <span
                        aria-label="down"
                        class="anticon anticon-down ant-select-suffix"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="down"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                          />
                        </svg>
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-col ant-col-8"
        style="padding-left:12px;padding-right:12px"
      >
        <div
          class="ant-form-item"
        >
          <div
            class="ant-row ant-form-item-row"
          >
            <div
              class="ant-col ant-form-item-label"
            >
              <label
                class="ant-form-item-required"
                for="advanced_search_field-2"
                title="Field 2"
              >
                Field 2
              </label>
            </div>
            <div
              class="ant-col ant-form-item-control"
            >
              <div
                class="ant-form-item-control-input"
              >
                <div
                  class="ant-form-item-control-input-content"
                >
                  <input
                    aria-required="true"
                    class="ant-input ant-input-outlined"
                    id="advanced_search_field-2"
                    placeholder="placeholder"
                    type="text"
                    value=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-col ant-col-8"
        style="padding-left:12px;padding-right:12px"
      >
        <div
          class="ant-form-item"
        >
          <div
            class="ant-row ant-form-item-row"
          >
            <div
              class="ant-col ant-form-item-label"
            >
              <label
                class="ant-form-item-required"
                for="advanced_search_field-3"
                title="Field 3"
              >
                Field 3
              </label>
            </div>
            <div
              class="ant-col ant-form-item-control"
            >
              <div
                class="ant-form-item-control-input"
              >
                <div
                  class="ant-form-item-control-input-content"
                >
                  <input
                    aria-required="true"
                    class="ant-input ant-input-outlined"
                    id="advanced_search_field-3"
                    placeholder="placeholder"
                    type="text"
                    value=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-col ant-col-8"
        style="padding-left:12px;padding-right:12px"
      >
        <div
          class="ant-form-item"
        >
          <div
            class="ant-row ant-form-item-row"
          >
            <div
              class="ant-col ant-form-item-label"
            >
              <label
                class="ant-form-item-required"
                for="advanced_search_field-4"
                title="Field 4"
              >
                Field 4
              </label>
            </div>
            <div
              class="ant-col ant-form-item-control"
            >
              <div
                class="ant-form-item-control-input"
              >
                <div
                  class="ant-form-item-control-input-content"
                >
                  <div
                    aria-required="true"
                    class="ant-select ant-select-outlined ant-select-in-form-item ant-select-single ant-select-show-arrow"
                  >
                    <div
                      class="ant-select-selector"
                    >
                      <span
                        class="ant-select-selection-wrap"
                      >
                        <span
                          class="ant-select-selection-search"
                        >
                          <input
                            aria-autocomplete="list"
                            aria-controls="advanced_search_field-4_list"
                            aria-expanded="false"
                            aria-haspopup="listbox"
                            aria-owns="advanced_search_field-4_list"
                            aria-required="true"
                            autocomplete="off"
                            class="ant-select-selection-search-input"
                            id="advanced_search_field-4"
                            readonly=""
                            role="combobox"
                            style="opacity:0"
                            type="search"
                            unselectable="on"
                            value=""
                          />
                        </span>
                        <span
                          class="ant-select-selection-item"
                          title="longlonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglong"
                        >
                          longlonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglonglong
                        </span>
                      </span>
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-arrow"
                      style="user-select:none;-webkit-user-select:none"
                      unselectable="on"
                    >
                      <span
                        aria-label="down"
                        class="anticon anticon-down ant-select-suffix"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="down"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                          />
                        </svg>
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="ant-col ant-col-8"
        style="padding-left:12px;padding-right:12px"
      >
        <div
          class="ant-form-item"
        >
          <div
            class="ant-row ant-form-item-row"
          >
            <div
              class="ant-col ant-form-item-label"
            >
              <label
                class="ant-form-item-required"
                for="advanced_search_field-5"
                title="Field 5"
              >
                Field 5
              </label>
            </div>
            <div
              class="ant-col ant-form-item-control"
            >
              <div
                class="ant-form-item-control-input"
              >
                <div
                  class="ant-form-item-control-input-content"
                >
                  <input
                    aria-required="true"
                    class="ant-input ant-input-outlined"
                    id="advanced_search_field-5"
                    placeholder="placeholder"
                    type="text"
                    value=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      style="text-align:right"
    >
      <div
        class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small"
      >
        <div
          class="ant-space-item"
        >
          <button
            class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
            type="submit"
          >
            <span>
              Search
            </span>
          </button>
        </div>
        <div
          class="ant-space-item"
        >
          <button
            class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
            type="button"
          >
            <span>
              Clear
            </span>
          </button>
        </div>
        <div
          class="ant-space-item"
        >
          <a
            style="font-size:12px"
          >
            <span
              aria-label="down"
              class="anticon anticon-down"
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="down"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                />
              </svg>
            </span>
             Collapse
          </a>
        </div>
      </div>
    </div>
  </form>,
  <div
    style="line-height:200px;text-align:center;background:rgba(0,0,0,0.02);border-radius:8px;margin-top:16px"
  >
    Search Result List
  </div>,
]
`;

exports[`renders components/form/demo/basic.tsx correctly 1`] = `
<form
  autocomplete="off"
  class="ant-form ant-form-horizontal"
  id="basic"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-8 ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="basic_username"
          title="Username"
        >
          Username
        </label>
      </div>
      <div
        class="ant-col ant-col-16 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              aria-required="true"
              class="ant-input ant-input-outlined"
              id="basic_username"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-8 ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="basic_password"
          title="Password"
        >
          Password
        </label>
      </div>
      <div
        class="ant-col ant-col-16 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined ant-input-password"
            >
              <input
                aria-required="true"
                class="ant-input"
                id="basic_password"
                type="password"
                value=""
              />
              <span
                class="ant-input-suffix"
              >
                <span
                  aria-label="eye-invisible"
                  class="anticon anticon-eye-invisible ant-input-password-icon"
                  role="img"
                  tabindex="-1"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="eye-invisible"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"
                    />
                    <path
                      d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-16 ant-col-offset-8 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <label
              class="ant-checkbox-wrapper ant-checkbox-wrapper-checked ant-checkbox-wrapper-in-form-item"
            >
              <span
                class="ant-checkbox ant-wave-target ant-checkbox-checked"
              >
                <input
                  checked=""
                  class="ant-checkbox-input"
                  id="basic_remember"
                  type="checkbox"
                />
                <span
                  class="ant-checkbox-inner"
                />
              </span>
              <span
                class="ant-checkbox-label"
              >
                Remember me
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-16 ant-col-offset-8 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              type="submit"
            >
              <span>
                Submit
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/col-24-debug.tsx correctly 1`] = `
Array [
  <form
    autocomplete="off"
    class="ant-form ant-form-horizontal"
    id="col-24-debug"
    style="max-width:600px"
  >
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-24 ant-form-item-label"
        >
          <label
            class="ant-form-item-required"
            for="col-24-debug_username"
            title="Username"
          >
            Username
          </label>
        </div>
        <div
          class="ant-col ant-col-24 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <input
                aria-required="true"
                class="ant-input ant-input-outlined"
                id="col-24-debug_username"
                type="text"
                value=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-24 ant-form-item-label"
        >
          <label
            class="ant-form-item-required"
            for="col-24-debug_password"
            title="Password"
          >
            Password
          </label>
        </div>
        <div
          class="ant-col ant-col-24 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <span
                class="ant-input-affix-wrapper ant-input-outlined ant-input-password"
              >
                <input
                  aria-required="true"
                  class="ant-input"
                  id="col-24-debug_password"
                  type="password"
                  value=""
                />
                <span
                  class="ant-input-suffix"
                >
                  <span
                    aria-label="eye-invisible"
                    class="anticon anticon-eye-invisible ant-input-password-icon"
                    role="img"
                    tabindex="-1"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="eye-invisible"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"
                      />
                      <path
                        d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"
                      />
                    </svg>
                  </span>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
      style="box-shadow:0 0 3px red"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-24 ant-form-item-label"
        >
          <label
            class=""
            for="col-24-debug_select"
            title=""
          >
            <a
              href="https://github.com/ant-design/ant-design/issues/36459"
              rel="noreferrer"
              target="_blank"
            >
              #36459
            </a>
          </label>
        </div>
        <div
          class="ant-col ant-col-24 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                class="ant-select ant-select-outlined ant-select-in-form-item ant-select-multiple ant-select-show-arrow ant-select-show-search"
                style="width:70%"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <div
                      class="ant-select-selection-overflow"
                    >
                      <div
                        class="ant-select-selection-overflow-item"
                        style="opacity:1"
                      >
                        <span
                          class="ant-select-selection-item"
                          title="Bamboo"
                        >
                          <span
                            class="ant-select-selection-item-content"
                          >
                            Bamboo
                          </span>
                          <span
                            aria-hidden="true"
                            class="ant-select-selection-item-remove"
                            style="user-select:none;-webkit-user-select:none"
                            unselectable="on"
                          >
                            <span
                              aria-label="close"
                              class="anticon anticon-close"
                              role="img"
                            >
                              <svg
                                aria-hidden="true"
                                data-icon="close"
                                fill="currentColor"
                                fill-rule="evenodd"
                                focusable="false"
                                height="1em"
                                viewBox="64 64 896 896"
                                width="1em"
                              >
                                <path
                                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                                />
                              </svg>
                            </span>
                          </span>
                        </span>
                      </div>
                      <div
                        class="ant-select-selection-overflow-item ant-select-selection-overflow-item-suffix"
                        style="opacity:1"
                      >
                        <div
                          class="ant-select-selection-search"
                          style="width:0"
                        >
                          <input
                            aria-autocomplete="list"
                            aria-controls="col-24-debug_select_list"
                            aria-expanded="false"
                            aria-haspopup="listbox"
                            aria-owns="col-24-debug_select_list"
                            autocomplete="off"
                            class="ant-select-selection-search-input"
                            id="col-24-debug_select"
                            readonly=""
                            role="combobox"
                            style="opacity:0"
                            type="search"
                            unselectable="on"
                            value=""
                          />
                          <span
                            aria-hidden="true"
                            class="ant-select-selection-search-mirror"
                          >
                          </span>
                        </div>
                      </div>
                    </div>
                  </span>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-arrow"
                  style="user-select:none;-webkit-user-select:none"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-select-suffix"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-24 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <button
                class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                type="submit"
              >
                <span>
                  Submit
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>,
  <form
    autocomplete="off"
    class="ant-form ant-form-horizontal"
    id="responsive"
  >
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-form-item-label ant-col-sm-24 ant-col-xl-24"
        >
          <label
            class="ant-form-item-required"
            for="responsive_username"
            title="Username"
          >
            Username
          </label>
        </div>
        <div
          class="ant-col ant-form-item-control ant-col-sm-24 ant-col-xl-24"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <input
                aria-required="true"
                class="ant-input ant-input-outlined"
                id="responsive_username"
                type="text"
                value=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-form-item-label ant-col-sm-24 ant-col-xl-24"
        >
          <label
            class="ant-form-item-required"
            for="responsive_password"
            title="Password"
          >
            Password
          </label>
        </div>
        <div
          class="ant-col ant-form-item-control ant-col-sm-24 ant-col-xl-24"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <span
                class="ant-input-affix-wrapper ant-input-outlined ant-input-password"
              >
                <input
                  aria-required="true"
                  class="ant-input"
                  id="responsive_password"
                  type="password"
                  value=""
                />
                <span
                  class="ant-input-suffix"
                >
                  <span
                    aria-label="eye-invisible"
                    class="anticon anticon-eye-invisible ant-input-password-icon"
                    role="img"
                    tabindex="-1"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="eye-invisible"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"
                      />
                      <path
                        d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"
                      />
                    </svg>
                  </span>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-form-item-control ant-col-sm-24 ant-col-xl-24"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <button
                class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                type="submit"
              >
                <span>
                  Submit
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>,
  <div
    class="ant-divider ant-divider-horizontal"
    role="separator"
  />,
  <form
    class="ant-form ant-form-vertical"
  >
    <div
      class="ant-form-item"
      style="box-shadow:0 0 3px red"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-form-item-label"
        >
          <label
            class=""
            for="select"
            title=""
          >
            <a
              href="https://github.com/ant-design/ant-design/issues/36459"
              rel="noreferrer"
              target="_blank"
            >
              #36459
            </a>
          </label>
        </div>
        <div
          class="ant-col ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                class="ant-select ant-select-outlined ant-select-in-form-item ant-select-multiple ant-select-show-arrow ant-select-show-search"
                style="width:70%"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <div
                      class="ant-select-selection-overflow"
                    >
                      <div
                        class="ant-select-selection-overflow-item"
                        style="opacity:1"
                      >
                        <span
                          class="ant-select-selection-item"
                          title="Bamboo"
                        >
                          <span
                            class="ant-select-selection-item-content"
                          >
                            Bamboo
                          </span>
                          <span
                            aria-hidden="true"
                            class="ant-select-selection-item-remove"
                            style="user-select:none;-webkit-user-select:none"
                            unselectable="on"
                          >
                            <span
                              aria-label="close"
                              class="anticon anticon-close"
                              role="img"
                            >
                              <svg
                                aria-hidden="true"
                                data-icon="close"
                                fill="currentColor"
                                fill-rule="evenodd"
                                focusable="false"
                                height="1em"
                                viewBox="64 64 896 896"
                                width="1em"
                              >
                                <path
                                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                                />
                              </svg>
                            </span>
                          </span>
                        </span>
                      </div>
                      <div
                        class="ant-select-selection-overflow-item ant-select-selection-overflow-item-suffix"
                        style="opacity:1"
                      >
                        <div
                          class="ant-select-selection-search"
                          style="width:0"
                        >
                          <input
                            aria-autocomplete="list"
                            aria-controls="select_list"
                            aria-expanded="false"
                            aria-haspopup="listbox"
                            aria-owns="select_list"
                            autocomplete="off"
                            class="ant-select-selection-search-input"
                            id="select"
                            readonly=""
                            role="combobox"
                            style="opacity:0"
                            type="search"
                            unselectable="on"
                            value=""
                          />
                          <span
                            aria-hidden="true"
                            class="ant-select-selection-search-mirror"
                          >
                          </span>
                        </div>
                      </div>
                    </div>
                  </span>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-arrow"
                  style="user-select:none;-webkit-user-select:none"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-select-suffix"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-12 ant-form-item-label"
        >
          <label
            class=""
            for="col12"
            title="col12"
          >
            col12
          </label>
        </div>
        <div
          class="ant-col ant-col-12 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <input
                class="ant-input ant-input-outlined"
                id="col12"
                type="text"
                value=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>,
]
`;

exports[`renders components/form/demo/control-hooks.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  id="control-hooks"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-8 ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="control-hooks_note"
          title="Note"
        >
          Note
        </label>
      </div>
      <div
        class="ant-col ant-col-16 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              aria-required="true"
              class="ant-input ant-input-outlined"
              id="control-hooks_note"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-8 ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="control-hooks_gender"
          title="Gender"
        >
          Gender
        </label>
      </div>
      <div
        class="ant-col ant-col-16 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              aria-required="true"
              class="ant-select ant-select-outlined ant-select-in-form-item ant-select-single ant-select-allow-clear ant-select-show-arrow"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="control-hooks_gender_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="control-hooks_gender_list"
                      aria-required="true"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      id="control-hooks_gender"
                      readonly=""
                      role="combobox"
                      style="opacity:0"
                      type="search"
                      unselectable="on"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  >
                    Select a option and change input text above
                  </span>
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-16 ant-col-offset-8 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small"
            >
              <div
                class="ant-space-item"
              >
                <button
                  class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                  type="submit"
                >
                  <span>
                    Submit
                  </span>
                </button>
              </div>
              <div
                class="ant-space-item"
              >
                <button
                  class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
                  type="button"
                >
                  <span>
                    Reset
                  </span>
                </button>
              </div>
              <div
                class="ant-space-item"
              >
                <button
                  class="ant-btn ant-btn-link ant-btn-color-link ant-btn-variant-link"
                  type="button"
                >
                  <span>
                    Fill form
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/custom-feedback-icons.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  id="custom-feedback-icons"
  style="max-width:600px"
>
  <div
    class="ant-form-item acss-140b0ev ant-form-item-with-help"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="custom-feedback-icons_custom-feedback-test-item"
          title="Test"
        >
          Test
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined"
            >
              <input
                aria-required="true"
                class="ant-input"
                id="custom-feedback-icons_custom-feedback-test-item"
                type="text"
                value=""
              />
              <span
                class="ant-input-suffix"
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item acss-140b0ev ant-form-item-with-help"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="custom-feedback-icons_custom-feedback-test-item2"
          title="Test"
        >
          Test
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined"
            >
              <input
                aria-required="true"
                class="ant-input"
                id="custom-feedback-icons_custom-feedback-test-item2"
                type="text"
                value=""
              />
              <span
                class="ant-input-suffix"
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item acss-140b0ev ant-form-item-has-feedback ant-form-item-has-success"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          for="custom-feedback-icons_custom-feedback-test-item3"
          title="Test"
        >
          Test
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-mentions-affix-wrapper ant-mentions-affix-wrapper-input-with-clear-btn ant-mentions-outlined ant-mentions-status-success"
            >
              <div
                class="ant-mentions"
              >
                <textarea
                  class="rc-textarea"
                  id="custom-feedback-icons_custom-feedback-test-item3"
                  rows="1"
                >
                  @mention1
                </textarea>
              </div>
              <span
                class="ant-mentions-suffix"
              >
                <button
                  class="ant-mentions-clear-icon ant-mentions-clear-icon-has-suffix"
                  tabindex="-1"
                  type="button"
                >
                  <span
                    aria-label="close-circle"
                    class="anticon anticon-close-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="close-circle"
                      fill="currentColor"
                      fill-rule="evenodd"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                      />
                    </svg>
                  </span>
                </button>
                <span
                  class="ant-form-item-feedback-icon ant-form-item-feedback-icon-success"
                >
                  <span
                    aria-label="check-circle"
                    class="anticon anticon-check-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="check-circle"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
              type="submit"
            >
              <span>
                Submit
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/customized-form-controls.tsx correctly 1`] = `
<form
  class="ant-form ant-form-inline"
  id="customized_form_controls"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          for="customized_form_controls_price"
          title="Price"
        >
          Price
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              id="customized_form_controls_price"
            >
              <input
                class="ant-input ant-input-outlined"
                style="width:100px"
                type="text"
                value="0"
              />
              <div
                class="ant-select ant-select-outlined ant-select-in-form-item ant-select-single ant-select-show-arrow"
                style="width:80px;margin:0 8px"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="undefined_list"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-owns="undefined_list"
                        autocomplete="off"
                        class="ant-select-selection-search-input"
                        readonly=""
                        role="combobox"
                        style="opacity:0"
                        type="search"
                        unselectable="on"
                        value=""
                      />
                    </span>
                    <span
                      class="ant-select-selection-item"
                      title="RMB"
                    >
                      RMB
                    </span>
                  </span>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-arrow"
                  style="user-select:none;-webkit-user-select:none"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-select-suffix"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              type="submit"
            >
              <span>
                Submit
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/disabled.tsx correctly 1`] = `
Array [
  <label
    class="ant-checkbox-wrapper ant-checkbox-wrapper-checked"
  >
    <span
      class="ant-checkbox ant-wave-target ant-checkbox-checked"
    >
      <input
        checked=""
        class="ant-checkbox-input"
        type="checkbox"
      />
      <span
        class="ant-checkbox-inner"
      />
    </span>
    <span
      class="ant-checkbox-label"
    >
      Form disabled
    </span>
  </label>,
  <form
    class="ant-form ant-form-horizontal"
    style="max-width:600px"
  >
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class=""
            for="disabled"
            title="Checkbox"
          >
            Checkbox
          </label>
        </div>
        <div
          class="ant-col ant-col-14 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <label
                class="ant-checkbox-wrapper ant-checkbox-wrapper-disabled ant-checkbox-wrapper-in-form-item"
              >
                <span
                  class="ant-checkbox ant-wave-target ant-checkbox-disabled"
                >
                  <input
                    class="ant-checkbox-input"
                    disabled=""
                    id="disabled"
                    type="checkbox"
                  />
                  <span
                    class="ant-checkbox-inner"
                  />
                </span>
                <span
                  class="ant-checkbox-label"
                >
                  Checkbox
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class=""
            title="Radio"
          >
            Radio
          </label>
        </div>
        <div
          class="ant-col ant-col-14 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                class="ant-radio-group ant-radio-group-outline"
              >
                <label
                  class="ant-radio-wrapper ant-radio-wrapper-disabled ant-radio-wrapper-in-form-item"
                >
                  <span
                    class="ant-radio ant-wave-target ant-radio-disabled"
                  >
                    <input
                      class="ant-radio-input"
                      disabled=""
                      name="test-id"
                      type="radio"
                      value="apple"
                    />
                    <span
                      class="ant-radio-inner"
                    />
                  </span>
                  <span
                    class="ant-radio-label"
                  >
                     Apple 
                  </span>
                </label>
                <label
                  class="ant-radio-wrapper ant-radio-wrapper-disabled ant-radio-wrapper-in-form-item"
                >
                  <span
                    class="ant-radio ant-wave-target ant-radio-disabled"
                  >
                    <input
                      class="ant-radio-input"
                      disabled=""
                      name="test-id"
                      type="radio"
                      value="pear"
                    />
                    <span
                      class="ant-radio-inner"
                    />
                  </span>
                  <span
                    class="ant-radio-label"
                  >
                     Pear 
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class=""
            title="Input"
          >
            Input
          </label>
        </div>
        <div
          class="ant-col ant-col-14 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <input
                class="ant-input ant-input-disabled ant-input-outlined"
                disabled=""
                type="text"
                value=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class=""
            title="Select"
          >
            Select
          </label>
        </div>
        <div
          class="ant-col ant-col-14 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                class="ant-select ant-select-outlined ant-select-in-form-item ant-select-single ant-select-show-arrow ant-select-disabled"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="undefined_list"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-owns="undefined_list"
                        autocomplete="off"
                        class="ant-select-selection-search-input"
                        disabled=""
                        readonly=""
                        role="combobox"
                        style="opacity:0"
                        type="search"
                        unselectable="on"
                        value=""
                      />
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-arrow"
                  style="user-select:none;-webkit-user-select:none"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-select-suffix"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class=""
            title="TreeSelect"
          >
            TreeSelect
          </label>
        </div>
        <div
          class="ant-col ant-col-14 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                class="ant-select ant-tree-select ant-select-outlined ant-select-in-form-item ant-select-single ant-select-show-arrow ant-select-disabled"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="undefined_list"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-owns="undefined_list"
                        autocomplete="off"
                        class="ant-select-selection-search-input"
                        disabled=""
                        readonly=""
                        role="combobox"
                        style="opacity:0"
                        type="search"
                        unselectable="on"
                        value=""
                      />
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-arrow"
                  style="user-select:none;-webkit-user-select:none"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-select-suffix"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class=""
            title="Cascader"
          >
            Cascader
          </label>
        </div>
        <div
          class="ant-col ant-col-14 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                class="ant-select ant-cascader ant-select-outlined ant-select-in-form-item ant-select-single ant-select-allow-clear ant-select-show-arrow ant-select-disabled"
              >
                <div
                  class="ant-select-selector"
                >
                  <span
                    class="ant-select-selection-wrap"
                  >
                    <span
                      class="ant-select-selection-search"
                    >
                      <input
                        aria-autocomplete="list"
                        aria-controls="undefined_list"
                        aria-expanded="false"
                        aria-haspopup="listbox"
                        aria-owns="undefined_list"
                        autocomplete="off"
                        class="ant-select-selection-search-input"
                        disabled=""
                        readonly=""
                        role="combobox"
                        style="opacity:0"
                        type="search"
                        unselectable="on"
                        value=""
                      />
                    </span>
                    <span
                      class="ant-select-selection-placeholder"
                    />
                  </span>
                </div>
                <span
                  aria-hidden="true"
                  class="ant-select-arrow"
                  style="user-select:none;-webkit-user-select:none"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-select-suffix"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class=""
            title="DatePicker"
          >
            DatePicker
          </label>
        </div>
        <div
          class="ant-col ant-col-14 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                class="ant-picker ant-picker-disabled ant-picker-outlined"
              >
                <div
                  class="ant-picker-input"
                >
                  <input
                    aria-invalid="false"
                    autocomplete="off"
                    disabled=""
                    placeholder="Select date"
                    size="12"
                    value=""
                  />
                  <span
                    class="ant-picker-suffix"
                  >
                    <span
                      aria-label="calendar"
                      class="anticon anticon-calendar"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="calendar"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
                        />
                      </svg>
                    </span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class=""
            title="RangePicker"
          >
            RangePicker
          </label>
        </div>
        <div
          class="ant-col ant-col-14 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                class="ant-picker ant-picker-range ant-picker-disabled ant-picker-outlined"
              >
                <div
                  class="ant-picker-input"
                >
                  <input
                    aria-invalid="false"
                    autocomplete="off"
                    date-range="start"
                    disabled=""
                    placeholder="Start date"
                    size="12"
                    value=""
                  />
                </div>
                <div
                  class="ant-picker-range-separator"
                >
                  <span
                    aria-label="to"
                    class="ant-picker-separator"
                  >
                    <span
                      aria-label="swap-right"
                      class="anticon anticon-swap-right"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="swap-right"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="0 0 1024 1024"
                        width="1em"
                      >
                        <path
                          d="M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z"
                        />
                      </svg>
                    </span>
                  </span>
                </div>
                <div
                  class="ant-picker-input"
                >
                  <input
                    aria-invalid="false"
                    autocomplete="off"
                    date-range="end"
                    disabled=""
                    placeholder="End date"
                    size="12"
                    value=""
                  />
                </div>
                <div
                  class="ant-picker-active-bar"
                  style="position:absolute;width:0"
                />
                <span
                  class="ant-picker-suffix"
                >
                  <span
                    aria-label="calendar"
                    class="anticon anticon-calendar"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="calendar"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class=""
            title="InputNumber"
          >
            InputNumber
          </label>
        </div>
        <div
          class="ant-col ant-col-14 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                class="ant-input-number ant-input-number-in-form-item ant-input-number-outlined ant-input-number-disabled"
              >
                <div
                  class="ant-input-number-handler-wrap"
                >
                  <span
                    aria-disabled="false"
                    aria-label="Increase Value"
                    class="ant-input-number-handler ant-input-number-handler-up"
                    role="button"
                    unselectable="on"
                  >
                    <span
                      aria-label="up"
                      class="anticon anticon-up ant-input-number-handler-up-inner"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="up"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                        />
                      </svg>
                    </span>
                  </span>
                  <span
                    aria-disabled="false"
                    aria-label="Decrease Value"
                    class="ant-input-number-handler ant-input-number-handler-down"
                    role="button"
                    unselectable="on"
                  >
                    <span
                      aria-label="down"
                      class="anticon anticon-down ant-input-number-handler-down-inner"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="down"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                        />
                      </svg>
                    </span>
                  </span>
                </div>
                <div
                  class="ant-input-number-input-wrap"
                >
                  <input
                    autocomplete="off"
                    class="ant-input-number-input"
                    disabled=""
                    role="spinbutton"
                    step="1"
                    value=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class=""
            title="TextArea"
          >
            TextArea
          </label>
        </div>
        <div
          class="ant-col ant-col-14 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <textarea
                class="ant-input ant-input-outlined ant-input-disabled"
                disabled=""
                rows="4"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class=""
            title="Switch"
          >
            Switch
          </label>
        </div>
        <div
          class="ant-col ant-col-14 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <button
                aria-checked="false"
                class="ant-switch ant-switch-disabled"
                disabled=""
                role="switch"
                type="button"
              >
                <div
                  class="ant-switch-handle"
                />
                <span
                  class="ant-switch-inner"
                >
                  <span
                    class="ant-switch-inner-checked"
                  />
                  <span
                    class="ant-switch-inner-unchecked"
                  />
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class=""
            title="Upload"
          >
            Upload
          </label>
        </div>
        <div
          class="ant-col ant-col-14 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <span
                class="ant-upload-wrapper ant-upload-picture-card-wrapper"
              >
                <div
                  class="ant-upload-list ant-upload-list-picture-card"
                >
                  <div
                    class="ant-upload ant-upload-select ant-upload-disabled"
                  >
                    <span
                      class="ant-upload ant-upload-disabled"
                    >
                      <input
                        accept=""
                        disabled=""
                        name="file"
                        style="display:none"
                        type="file"
                      />
                      <button
                        style="color:inherit;cursor:inherit;border:0;background:none"
                        type="button"
                      >
                        <span
                          aria-label="plus"
                          class="anticon anticon-plus"
                          role="img"
                        >
                          <svg
                            aria-hidden="true"
                            data-icon="plus"
                            fill="currentColor"
                            focusable="false"
                            height="1em"
                            viewBox="64 64 896 896"
                            width="1em"
                          >
                            <path
                              d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"
                            />
                            <path
                              d="M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"
                            />
                          </svg>
                        </span>
                        <div
                          style="margin-top:8px"
                        >
                          Upload
                        </div>
                      </button>
                    </span>
                  </div>
                </div>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class=""
            title="Button"
          >
            Button
          </label>
        </div>
        <div
          class="ant-col ant-col-14 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <button
                class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
                disabled=""
                type="button"
              >
                <span>
                  Button
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class=""
            title="Slider"
          >
            Slider
          </label>
        </div>
        <div
          class="ant-col ant-col-14 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                class="ant-slider ant-slider-disabled ant-slider-horizontal"
              >
                <div
                  class="ant-slider-rail"
                />
                <div
                  class="ant-slider-track"
                  style="left:0%;width:0%"
                />
                <div
                  class="ant-slider-step"
                />
                <div
                  aria-describedby="test-id"
                  aria-disabled="true"
                  aria-orientation="horizontal"
                  aria-valuemax="100"
                  aria-valuemin="0"
                  aria-valuenow="0"
                  class="ant-slider-handle"
                  role="slider"
                  style="left:0%;transform:translateX(-50%)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class=""
            title="ColorPicker"
          >
            ColorPicker
          </label>
        </div>
        <div
          class="ant-col ant-col-14 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                aria-describedby="test-id"
                class="ant-color-picker-trigger ant-color-picker-trigger-disabled"
              >
                <div
                  class="ant-color-picker-clear"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class=""
            title="Rate"
          >
            Rate
          </label>
        </div>
        <div
          class="ant-col ant-col-14 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <ul
                class="ant-rate ant-rate-disabled"
                tabindex="-1"
              >
                <li
                  class="ant-rate-star ant-rate-star-zero"
                >
                  <div
                    aria-checked="false"
                    aria-posinset="1"
                    aria-setsize="5"
                    role="radio"
                    tabindex="-1"
                  >
                    <div
                      class="ant-rate-star-first"
                    >
                      <span
                        aria-label="star"
                        class="anticon anticon-star"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="star"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                          />
                        </svg>
                      </span>
                    </div>
                    <div
                      class="ant-rate-star-second"
                    >
                      <span
                        aria-label="star"
                        class="anticon anticon-star"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="star"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                          />
                        </svg>
                      </span>
                    </div>
                  </div>
                </li>
                <li
                  class="ant-rate-star ant-rate-star-zero"
                >
                  <div
                    aria-checked="false"
                    aria-posinset="2"
                    aria-setsize="5"
                    role="radio"
                    tabindex="-1"
                  >
                    <div
                      class="ant-rate-star-first"
                    >
                      <span
                        aria-label="star"
                        class="anticon anticon-star"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="star"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                          />
                        </svg>
                      </span>
                    </div>
                    <div
                      class="ant-rate-star-second"
                    >
                      <span
                        aria-label="star"
                        class="anticon anticon-star"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="star"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                          />
                        </svg>
                      </span>
                    </div>
                  </div>
                </li>
                <li
                  class="ant-rate-star ant-rate-star-zero"
                >
                  <div
                    aria-checked="false"
                    aria-posinset="3"
                    aria-setsize="5"
                    role="radio"
                    tabindex="-1"
                  >
                    <div
                      class="ant-rate-star-first"
                    >
                      <span
                        aria-label="star"
                        class="anticon anticon-star"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="star"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                          />
                        </svg>
                      </span>
                    </div>
                    <div
                      class="ant-rate-star-second"
                    >
                      <span
                        aria-label="star"
                        class="anticon anticon-star"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="star"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                          />
                        </svg>
                      </span>
                    </div>
                  </div>
                </li>
                <li
                  class="ant-rate-star ant-rate-star-zero"
                >
                  <div
                    aria-checked="false"
                    aria-posinset="4"
                    aria-setsize="5"
                    role="radio"
                    tabindex="-1"
                  >
                    <div
                      class="ant-rate-star-first"
                    >
                      <span
                        aria-label="star"
                        class="anticon anticon-star"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="star"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                          />
                        </svg>
                      </span>
                    </div>
                    <div
                      class="ant-rate-star-second"
                    >
                      <span
                        aria-label="star"
                        class="anticon anticon-star"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="star"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                          />
                        </svg>
                      </span>
                    </div>
                  </div>
                </li>
                <li
                  class="ant-rate-star ant-rate-star-zero"
                >
                  <div
                    aria-checked="false"
                    aria-posinset="5"
                    aria-setsize="5"
                    role="radio"
                    tabindex="-1"
                  >
                    <div
                      class="ant-rate-star-first"
                    >
                      <span
                        aria-label="star"
                        class="anticon anticon-star"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="star"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                          />
                        </svg>
                      </span>
                    </div>
                    <div
                      class="ant-rate-star-second"
                    >
                      <span
                        aria-label="star"
                        class="anticon anticon-star"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="star"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                          />
                        </svg>
                      </span>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>,
]
`;

exports[`renders components/form/demo/disabled-input-debug.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          title="Normal0"
        >
          Normal0
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              placeholder="unavailable choice"
              type="text"
              value="Buggy!"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-with-help ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          title="Fail0"
        >
          Fail0
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined ant-input-status-error"
              placeholder="unavailable choice"
              type="text"
              value="Buggy!"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-with-help ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          title="FailDisabled0"
        >
          FailDisabled0
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-disabled ant-input-outlined ant-input-status-error"
              disabled=""
              placeholder="unavailable choice"
              type="text"
              value="Buggy!"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          title="Normal1"
        >
          Normal1
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              placeholder="unavailable choice"
              type="text"
              value="Buggy!"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-with-help ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          title="Fail1"
        >
          Fail1
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined ant-input-status-error"
              placeholder="unavailable choice"
              type="text"
              value="Buggy!"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-with-help ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          title="FailDisabled1"
        >
          FailDisabled1
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-disabled ant-input-outlined ant-input-status-error"
              disabled=""
              placeholder="unavailable choice"
              type="text"
              value="Buggy!"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          title="Normal2"
        >
          Normal2
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-group-wrapper ant-input-group-wrapper-outlined"
            >
              <span
                class="ant-input-wrapper ant-input-group"
              >
                <span
                  class="ant-input-group-addon"
                >
                  Buggy!
                </span>
                <input
                  class="ant-input ant-input-outlined"
                  placeholder="unavailable choice"
                  type="text"
                  value=""
                />
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-with-help ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          title="Fail2"
        >
          Fail2
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-group-wrapper ant-input-group-wrapper-outlined ant-input-group-wrapper-status-error"
            >
              <span
                class="ant-input-wrapper ant-input-group"
              >
                <span
                  class="ant-input-group-addon"
                >
                  Buggy!
                </span>
                <input
                  class="ant-input ant-input-outlined ant-input-status-error"
                  placeholder="unavailable choice"
                  type="text"
                  value=""
                />
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-with-help ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          title="FailDisabled2"
        >
          FailDisabled2
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-group-wrapper ant-input-group-wrapper-disabled ant-input-group-wrapper-outlined ant-input-group-wrapper-status-error"
            >
              <span
                class="ant-input-wrapper ant-input-group"
              >
                <span
                  class="ant-input-group-addon"
                >
                  Buggy!
                </span>
                <input
                  class="ant-input ant-input-disabled ant-input-outlined ant-input-status-error"
                  disabled=""
                  placeholder="unavailable choice"
                  type="text"
                  value=""
                />
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          title="Normal3"
        >
          Normal3
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined"
            >
              <span
                class="ant-input-prefix"
              >
                人民币
              </span>
              <input
                class="ant-input"
                placeholder="unavailable choice"
                type="text"
                value="50"
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-with-help ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          title="Fail3"
        >
          Fail3
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined ant-input-status-error"
            >
              <span
                class="ant-input-prefix"
              >
                人民币
              </span>
              <input
                class="ant-input"
                placeholder="unavailable choice"
                type="text"
                value="50"
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-with-help ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          title="FailDisabled3"
        >
          FailDisabled3
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-disabled ant-input-affix-wrapper-disabled ant-input-outlined ant-input-status-error"
            >
              <span
                class="ant-input-prefix"
              >
                人民币
              </span>
              <input
                class="ant-input ant-input-disabled"
                disabled=""
                placeholder="unavailable choice"
                type="text"
                value="50"
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/dynamic-form-item.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  id="dynamic_form_item"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-xs-offset-0 ant-col-sm-20 ant-col-sm-offset-4"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed"
              style="width:60%"
              type="button"
            >
              <span
                class="ant-btn-icon"
              >
                <span
                  aria-label="plus"
                  class="anticon anticon-plus"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="plus"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"
                    />
                    <path
                      d="M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"
                    />
                  </svg>
                </span>
              </span>
              <span>
                Add field
              </span>
            </button>
            <button
              class="ant-btn ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed"
              style="width:60%;margin-top:20px"
              type="button"
            >
              <span
                class="ant-btn-icon"
              >
                <span
                  aria-label="plus"
                  class="anticon anticon-plus"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="plus"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"
                    />
                    <path
                      d="M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"
                    />
                  </svg>
                </span>
              </span>
              <span>
                Add field at head
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-xs-offset-0 ant-col-sm-20 ant-col-sm-offset-4"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              type="submit"
            >
              <span>
                Submit
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/dynamic-form-items.tsx correctly 1`] = `
<form
  autocomplete="off"
  class="ant-form ant-form-horizontal"
  id="dynamic_form_nest_item"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed ant-btn-block"
              type="button"
            >
              <span
                class="ant-btn-icon"
              >
                <span
                  aria-label="plus"
                  class="anticon anticon-plus"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="plus"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"
                    />
                    <path
                      d="M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"
                    />
                  </svg>
                </span>
              </span>
              <span>
                Add field
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              type="submit"
            >
              <span>
                Submit
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/dynamic-form-items-complex.tsx correctly 1`] = `
<form
  autocomplete="off"
  class="ant-form ant-form-horizontal"
  id="dynamic_form_complex"
  style="max-width:600px"
>
  <div
    style="display:flex;row-gap:16px;flex-direction:column"
  >
    <div
      class="ant-card ant-card-bordered ant-card-small"
    >
      <div
        class="ant-card-head"
      >
        <div
          class="ant-card-head-wrapper"
        >
          <div
            class="ant-card-head-title"
          >
            Item 1
          </div>
          <div
            class="ant-card-extra"
          >
            <span
              aria-label="close"
              class="anticon anticon-close"
              role="img"
              tabindex="-1"
            >
              <svg
                aria-hidden="true"
                data-icon="close"
                fill="currentColor"
                fill-rule="evenodd"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                />
              </svg>
            </span>
          </div>
        </div>
      </div>
      <div
        class="ant-card-body"
      >
        <div
          class="ant-form-item"
        >
          <div
            class="ant-row ant-form-item-row"
          >
            <div
              class="ant-col ant-col-6 ant-form-item-label"
            >
              <label
                class=""
                for="dynamic_form_complex_items_0_name"
                title="Name"
              >
                Name
              </label>
            </div>
            <div
              class="ant-col ant-col-18 ant-form-item-control"
            >
              <div
                class="ant-form-item-control-input"
              >
                <div
                  class="ant-form-item-control-input-content"
                >
                  <input
                    class="ant-input ant-input-outlined"
                    id="dynamic_form_complex_items_0_name"
                    type="text"
                    value=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="ant-form-item"
        >
          <div
            class="ant-row ant-form-item-row"
          >
            <div
              class="ant-col ant-col-6 ant-form-item-label"
            >
              <label
                class=""
                title="List"
              >
                List
              </label>
            </div>
            <div
              class="ant-col ant-col-18 ant-form-item-control"
            >
              <div
                class="ant-form-item-control-input"
              >
                <div
                  class="ant-form-item-control-input-content"
                >
                  <div
                    style="display:flex;flex-direction:column;row-gap:16px"
                  >
                    <button
                      class="ant-btn ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed ant-btn-block"
                      type="button"
                    >
                      <span>
                        + Add Sub Item
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <button
      class="ant-btn ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed ant-btn-block"
      type="button"
    >
      <span>
        + Add Item
      </span>
    </button>
  </div>
  <article
    class="ant-typography"
  >
    <pre>
      {}
    </pre>
  </article>
</form>
`;

exports[`renders components/form/demo/dynamic-form-items-no-style.tsx correctly 1`] = `
<form
  autocomplete="off"
  class="ant-form ant-form-horizontal"
  id="dynamic_form_no_style"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          title="Users"
        >
          Users
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-form-item"
            >
              <div
                class="ant-row ant-form-item-row"
              >
                <div
                  class="ant-col ant-form-item-control"
                >
                  <div
                    class="ant-form-item-control-input"
                  >
                    <div
                      class="ant-form-item-control-input-content"
                    >
                      <button
                        class="ant-btn ant-btn-dashed ant-btn-color-default ant-btn-variant-dashed ant-btn-block"
                        type="button"
                      >
                        <span
                          class="ant-btn-icon"
                        >
                          <span
                            aria-label="plus"
                            class="anticon anticon-plus"
                            role="img"
                          >
                            <svg
                              aria-hidden="true"
                              data-icon="plus"
                              fill="currentColor"
                              focusable="false"
                              height="1em"
                              viewBox="64 64 896 896"
                              width="1em"
                            >
                              <path
                                d="M482 152h60q8 0 8 8v704q0 8-8 8h-60q-8 0-8-8V160q0-8 8-8z"
                              />
                              <path
                                d="M192 474h672q8 0 8 8v60q0 8-8 8H160q-8 0-8-8v-60q0-8 8-8z"
                              />
                            </svg>
                          </span>
                        </span>
                        <span>
                          Add field
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              type="submit"
            >
              <span>
                Submit
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/dynamic-rule.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  id="dynamic_rule"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-4 ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="dynamic_rule_username"
          title="Name"
        >
          Name
        </label>
      </div>
      <div
        class="ant-col ant-col-8 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              aria-required="true"
              class="ant-input ant-input-outlined"
              id="dynamic_rule_username"
              placeholder="Please input your name"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-4 ant-form-item-label"
      >
        <label
          class=""
          for="dynamic_rule_nickname"
          title="Nickname"
        >
          Nickname
        </label>
      </div>
      <div
        class="ant-col ant-col-8 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              id="dynamic_rule_nickname"
              placeholder="Please input your nickname"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-8 ant-col-offset-4 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <label
              class="ant-checkbox-wrapper ant-checkbox-wrapper-in-form-item"
            >
              <span
                class="ant-checkbox ant-wave-target"
              >
                <input
                  class="ant-checkbox-input"
                  type="checkbox"
                />
                <span
                  class="ant-checkbox-inner"
                />
              </span>
              <span
                class="ant-checkbox-label"
              >
                Nickname is required
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-8 ant-col-offset-4 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              type="button"
            >
              <span>
                Check
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/form-context.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  id="basicForm"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-8 ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="basicForm_group"
          title="Group Name"
        >
          Group Name
        </label>
      </div>
      <div
        class="ant-col ant-col-16 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              aria-required="true"
              class="ant-input ant-input-outlined"
              id="basicForm_group"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-8 ant-form-item-label"
      >
        <label
          class=""
          title="User List"
        >
          User List
        </label>
      </div>
      <div
        class="ant-col ant-col-16 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-typography ant-typography-secondary ant-form-text"
            >
              ( 
              <span
                aria-label="smile"
                class="anticon anticon-smile"
                role="img"
              >
                <svg
                  aria-hidden="true"
                  data-icon="smile"
                  fill="currentColor"
                  focusable="false"
                  height="1em"
                  viewBox="64 64 896 896"
                  width="1em"
                >
                  <path
                    d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
                  />
                </svg>
              </span>
               No user yet. )
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-16 ant-col-offset-8 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              type="submit"
            >
              <span>
                Submit
              </span>
            </button>
            <button
              class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
              style="margin:0 8px"
              type="button"
            >
              <span>
                Add User
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/form-dependencies.tsx correctly 1`] = `
<form
  autocomplete="off"
  class="ant-form ant-form-vertical"
  id="dependencies"
  style="max-width:600px"
>
  <div
    class="ant-alert ant-alert-info"
    data-show="true"
    role="alert"
  >
    <span
      aria-label="info-circle"
      class="anticon anticon-info-circle ant-alert-icon"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="info-circle"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
        />
      </svg>
    </span>
    <div
      class="ant-alert-content"
    >
      <div
        class="ant-alert-message"
      >
         Try modify \`Password2\` and then modify \`Password\`
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="dependencies_password"
          title="Password"
        >
          Password
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              aria-required="true"
              class="ant-input ant-input-outlined"
              id="dependencies_password"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="dependencies_password2"
          title="Confirm Password"
        >
          Confirm Password
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              aria-required="true"
              class="ant-input ant-input-outlined"
              id="dependencies_password2"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <article
    class="ant-typography"
  >
    <p>
      Only Update when 
      <code>
        password2
      </code>
       updated:
    </p>
    <pre>
      {}
    </pre>
  </article>
</form>
`;

exports[`renders components/form/demo/form-in-modal.tsx correctly 1`] = `
Array [
  <button
    class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="button"
  >
    <span>
      New Collection
    </span>
  </button>,
  <pre />,
]
`;

exports[`renders components/form/demo/form-item-path.tsx correctly 1`] = `
<form
  class="ant-form ant-form-vertical"
  id="form_item_path"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          for="form_item_path_user_name_firstName"
          title="First Name"
        >
          First Name
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              id="form_item_path_user_name_firstName"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          for="form_item_path_user_name_lastName"
          title="Last Name"
        >
          Last Name
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              id="form_item_path_user_name_lastName"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          for="form_item_path_user_age"
          title="Age"
        >
          Age
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              id="form_item_path_user_age"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <button
    class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
    type="submit"
  >
    <span>
      Submit
    </span>
  </button>
</form>
`;

exports[`renders components/form/demo/getValueProps-normalize.tsx correctly 1`] = `
<form
  autocomplete="off"
  class="ant-form ant-form-horizontal"
  id="getValueProps"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-8 ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="getValueProps_date"
          title="Date"
        >
          Date
        </label>
      </div>
      <div
        class="ant-col ant-col-16 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-picker ant-picker-outlined"
            >
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  aria-required="true"
                  autocomplete="off"
                  id="getValueProps_date"
                  placeholder="Select date"
                  size="12"
                  value="2024-01-01"
                />
                <span
                  class="ant-picker-suffix"
                >
                  <span
                    aria-label="calendar"
                    class="anticon anticon-calendar"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="calendar"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
                      />
                    </svg>
                  </span>
                </span>
                <span
                  class="ant-picker-clear"
                  role="button"
                >
                  <span
                    aria-label="close-circle"
                    class="anticon anticon-close-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="close-circle"
                      fill="currentColor"
                      fill-rule="evenodd"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-16 ant-col-offset-8 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              type="submit"
            >
              <span>
                Submit
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/global-state.tsx correctly 1`] = `
Array [
  <form
    class="ant-form ant-form-inline"
    id="global_state"
  >
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-form-item-label"
        >
          <label
            class="ant-form-item-required"
            for="global_state_username"
            title="Username"
          >
            Username
          </label>
        </div>
        <div
          class="ant-col ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <input
                aria-required="true"
                class="ant-input ant-input-outlined"
                id="global_state_username"
                type="text"
                value=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>,
  <div
    class="ant-typography"
    style="max-width:440px;margin-top:24px"
  >
    <pre
      style="border:none"
    >
      [
  {
    "name": [
      "username"
    ],
    "value": "Ant Design"
  }
]
    </pre>
  </div>,
]
`;

exports[`renders components/form/demo/inline-login.tsx correctly 1`] = `
<form
  class="ant-form ant-form-inline"
  id="horizontal_login"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined"
            >
              <span
                class="ant-input-prefix"
              >
                <span
                  aria-label="user"
                  class="anticon anticon-user"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="user"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
                    />
                  </svg>
                </span>
              </span>
              <input
                aria-required="true"
                class="ant-input"
                id="horizontal_login_username"
                placeholder="Username"
                type="text"
                value=""
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined"
            >
              <span
                class="ant-input-prefix"
              >
                <span
                  aria-label="lock"
                  class="anticon anticon-lock"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="lock"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"
                    />
                  </svg>
                </span>
              </span>
              <input
                aria-required="true"
                class="ant-input"
                id="horizontal_login_password"
                placeholder="Password"
                type="password"
                value=""
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              disabled=""
              type="submit"
            >
              <span>
                Log in
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/label-debug.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  id="label-ellipsis"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-8 ant-form-item-label"
      >
        <label
          class=""
          for="label-ellipsis_username"
          title=""
        >
          <span
            class="ant-typography ant-typography-ellipsis ant-typography-ellipsis-single-line"
          >
            longtextlongtextlongtextlongtextlongtextlongtextlongtext
          </span>
        </label>
      </div>
      <div
        class="ant-col ant-col-16 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              id="label-ellipsis_username"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-8 ant-form-item-label"
      >
        <label
          class=""
          for="label-ellipsis_password"
          title=""
        >
          <span
            class="ant-typography ant-typography-ellipsis ant-typography-ellipsis-single-line"
          >
            longtext longtext longtext longtext longtext longtext longtext
          </span>
        </label>
      </div>
      <div
        class="ant-col ant-col-16 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined ant-input-password"
            >
              <input
                class="ant-input"
                id="label-ellipsis_password"
                type="password"
                value=""
              />
              <span
                class="ant-input-suffix"
              >
                <span
                  aria-label="eye-invisible"
                  class="anticon anticon-eye-invisible ant-input-password-icon"
                  role="img"
                  tabindex="-1"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="eye-invisible"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"
                    />
                    <path
                      d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/layout.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          for="layout"
          title="Form Layout"
        >
          Form Layout
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-radio-group ant-radio-group-outline"
              id="layout"
            >
              <label
                class="ant-radio-button-wrapper ant-radio-button-wrapper-checked ant-radio-button-wrapper-in-form-item"
              >
                <span
                  class="ant-radio-button ant-radio-button-checked"
                >
                  <input
                    checked=""
                    class="ant-radio-button-input"
                    name="layout"
                    type="radio"
                    value="horizontal"
                  />
                  <span
                    class="ant-radio-button-inner"
                  />
                </span>
                <span
                  class="ant-radio-button-label"
                >
                  Horizontal
                </span>
              </label>
              <label
                class="ant-radio-button-wrapper ant-radio-button-wrapper-in-form-item"
              >
                <span
                  class="ant-radio-button"
                >
                  <input
                    class="ant-radio-button-input"
                    name="layout"
                    type="radio"
                    value="vertical"
                  />
                  <span
                    class="ant-radio-button-inner"
                  />
                </span>
                <span
                  class="ant-radio-button-label"
                >
                  Vertical
                </span>
              </label>
              <label
                class="ant-radio-button-wrapper ant-radio-button-wrapper-in-form-item"
              >
                <span
                  class="ant-radio-button"
                >
                  <input
                    class="ant-radio-button-input"
                    name="layout"
                    type="radio"
                    value="inline"
                  />
                  <span
                    class="ant-radio-button-inner"
                  />
                </span>
                <span
                  class="ant-radio-button-label"
                >
                  Inline
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          title="Field A"
        >
          Field A
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              placeholder="input placeholder"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          title="Field B"
        >
          Field B
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              placeholder="input placeholder"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              type="button"
            >
              <span>
                Submit
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/layout-can-wrap.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  id="wrap"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-form-item-label-left ant-form-item-label-wrap"
        style="flex:0 0 110px"
      >
        <label
          class="ant-form-item-required ant-form-item-no-colon"
          for="wrap_username"
          title="Normal label"
        >
          Normal label
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
        style="flex:1 1 auto"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              aria-required="true"
              class="ant-input ant-input-outlined"
              id="wrap_username"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-form-item-label-left ant-form-item-label-wrap"
        style="flex:0 0 110px"
      >
        <label
          class="ant-form-item-required ant-form-item-no-colon"
          for="wrap_password"
          title="A super long label text"
        >
          A super long label text
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
        style="flex:1 1 auto"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              aria-required="true"
              class="ant-input ant-input-outlined"
              id="wrap_password"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-form-item-label-left ant-form-item-label-wrap"
        style="flex:0 0 110px"
      >
        <label
          class="ant-form-item-no-colon"
          for="wrap_password1"
          title="A super long label text"
        >
          A super long label text
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
        style="flex:1 1 auto"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              id="wrap_password1"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-form-item-label-left ant-form-item-label-wrap"
        style="flex:0 0 110px"
      >
        <label
          class="ant-form-item-no-colon"
          title=" "
        >
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
        style="flex:1 1 auto"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              type="submit"
            >
              <span>
                Submit
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/layout-multiple.tsx correctly 1`] = `
Array [
  <form
    class="ant-form ant-form-horizontal"
    id="layout-multiple-horizontal"
  >
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class="ant-form-item-required"
            for="layout-multiple-horizontal_horizontal"
            title="horizontal"
          >
            horizontal
          </label>
        </div>
        <div
          class="ant-col ant-col-20 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <input
                aria-required="true"
                class="ant-input ant-input-outlined"
                id="layout-multiple-horizontal_horizontal"
                type="text"
                value=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item ant-form-item-vertical"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-24 ant-form-item-label"
        >
          <label
            class="ant-form-item-required"
            for="layout-multiple-horizontal_vertical"
            title="vertical"
          >
            vertical
          </label>
        </div>
        <div
          class="ant-col ant-col-24 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <input
                aria-required="true"
                class="ant-input ant-input-outlined"
                id="layout-multiple-horizontal_vertical"
                type="text"
                value=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>,
  <br />,
  <form
    class="ant-form ant-form-vertical"
    id="layout-multiple-vertical"
  >
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class="ant-form-item-required"
            for="layout-multiple-vertical_vertical"
            title="vertical"
          >
            vertical
          </label>
        </div>
        <div
          class="ant-col ant-col-20 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <input
                aria-required="true"
                class="ant-input ant-input-outlined"
                id="layout-multiple-vertical_vertical"
                type="text"
                value=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item ant-form-item-horizontal"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-col-4 ant-form-item-label"
        >
          <label
            class="ant-form-item-required"
            for="layout-multiple-vertical_horizontal"
            title="horizontal"
          >
            horizontal
          </label>
        </div>
        <div
          class="ant-col ant-col-20 ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <input
                aria-required="true"
                class="ant-input ant-input-outlined"
                id="layout-multiple-vertical_horizontal"
                type="text"
                value=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>,
]
`;

exports[`renders components/form/demo/login.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  id="login"
  style="max-width:360px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined"
            >
              <span
                class="ant-input-prefix"
              >
                <span
                  aria-label="user"
                  class="anticon anticon-user"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="user"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M858.5 763.6a374 374 0 00-80.6-119.5 375.63 375.63 0 00-119.5-80.6c-.4-.2-.8-.3-1.2-.5C719.5 518 760 444.7 760 362c0-137-111-248-248-248S264 225 264 362c0 82.7 40.5 156 102.8 201.1-.4.2-.8.3-1.2.5-44.8 18.9-85 46-119.5 80.6a375.63 375.63 0 00-80.6 119.5A371.7 371.7 0 00136 901.8a8 8 0 008 8.2h60c4.4 0 7.9-3.5 8-7.8 2-77.2 33-149.5 87.8-204.3 56.7-56.7 132-87.9 212.2-87.9s155.5 31.2 212.2 87.9C779 752.7 810 825 812 902.2c.1 4.4 3.6 7.8 8 7.8h60a8 8 0 008-8.2c-1-47.8-10.9-94.3-29.5-138.2zM512 534c-45.9 0-89.1-17.9-121.6-50.4S340 407.9 340 362c0-45.9 17.9-89.1 50.4-121.6S466.1 190 512 190s89.1 17.9 121.6 50.4S684 316.1 684 362c0 45.9-17.9 89.1-50.4 121.6S557.9 534 512 534z"
                    />
                  </svg>
                </span>
              </span>
              <input
                aria-required="true"
                class="ant-input"
                id="login_username"
                placeholder="Username"
                type="text"
                value=""
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined"
            >
              <span
                class="ant-input-prefix"
              >
                <span
                  aria-label="lock"
                  class="anticon anticon-lock"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="lock"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 10-56 0z"
                    />
                  </svg>
                </span>
              </span>
              <input
                aria-required="true"
                class="ant-input"
                id="login_password"
                placeholder="Password"
                type="password"
                value=""
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-flex ant-flex-align-center ant-flex-justify-space-between"
            >
              <label
                class="ant-checkbox-wrapper ant-checkbox-wrapper-checked ant-checkbox-wrapper-in-form-item"
              >
                <span
                  class="ant-checkbox ant-wave-target ant-checkbox-checked"
                >
                  <input
                    checked=""
                    class="ant-checkbox-input"
                    id="login_remember"
                    type="checkbox"
                  />
                  <span
                    class="ant-checkbox-inner"
                  />
                </span>
                <span
                  class="ant-checkbox-label"
                >
                  Remember me
                </span>
              </label>
              <a
                href=""
              >
                Forgot password
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid ant-btn-block"
              type="submit"
            >
              <span>
                Log in
              </span>
            </button>
            or 
            <a
              href=""
            >
              Register now!
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/nest-messages.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  id="nest-messages"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-8 ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="nest-messages_user_name"
          title="Name"
        >
          Name
        </label>
      </div>
      <div
        class="ant-col ant-col-16 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              aria-required="true"
              class="ant-input ant-input-outlined"
              id="nest-messages_user_name"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-8 ant-form-item-label"
      >
        <label
          class=""
          for="nest-messages_user_email"
          title="Email"
        >
          Email
        </label>
      </div>
      <div
        class="ant-col ant-col-16 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              id="nest-messages_user_email"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-8 ant-form-item-label"
      >
        <label
          class=""
          for="nest-messages_user_age"
          title="Age"
        >
          Age
        </label>
      </div>
      <div
        class="ant-col ant-col-16 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-input-number ant-input-number-in-form-item ant-input-number-outlined"
            >
              <div
                class="ant-input-number-handler-wrap"
              >
                <span
                  aria-disabled="false"
                  aria-label="Increase Value"
                  class="ant-input-number-handler ant-input-number-handler-up"
                  role="button"
                  unselectable="on"
                >
                  <span
                    aria-label="up"
                    class="anticon anticon-up ant-input-number-handler-up-inner"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="up"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                      />
                    </svg>
                  </span>
                </span>
                <span
                  aria-disabled="false"
                  aria-label="Decrease Value"
                  class="ant-input-number-handler ant-input-number-handler-down"
                  role="button"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-input-number-handler-down-inner"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-input-number-input-wrap"
              >
                <input
                  autocomplete="off"
                  class="ant-input-number-input"
                  id="nest-messages_user_age"
                  role="spinbutton"
                  step="1"
                  value=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-8 ant-form-item-label"
      >
        <label
          class=""
          for="nest-messages_user_website"
          title="Website"
        >
          Website
        </label>
      </div>
      <div
        class="ant-col ant-col-16 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              id="nest-messages_user_website"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-8 ant-form-item-label"
      >
        <label
          class=""
          for="nest-messages_user_introduction"
          title="Introduction"
        >
          Introduction
        </label>
      </div>
      <div
        class="ant-col ant-col-16 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <textarea
              class="ant-input ant-input-outlined"
              id="nest-messages_user_introduction"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-16 ant-col-offset-8 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              type="submit"
            >
              <span>
                Submit
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/ref-item.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          for="test"
          title="test"
        >
          test
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              id="test"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              id="list_0"
              type="text"
              value="light"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <button
    class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
    type="button"
  >
    <span>
      Focus Form.Item
    </span>
  </button>
  <button
    class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
    type="button"
  >
    <span>
      Focus Form.List
    </span>
  </button>
</form>
`;

exports[`renders components/form/demo/register.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  id="register"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class="ant-form-item-required"
          for="register_email"
          title="E-mail"
        >
          E-mail
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              aria-required="true"
              class="ant-input ant-input-outlined"
              id="register_email"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class="ant-form-item-required"
          for="register_password"
          title="Password"
        >
          Password
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined ant-input-password"
            >
              <input
                aria-required="true"
                class="ant-input"
                id="register_password"
                type="password"
                value=""
              />
              <span
                class="ant-input-suffix"
              >
                <span
                  aria-label="eye-invisible"
                  class="anticon anticon-eye-invisible ant-input-password-icon"
                  role="img"
                  tabindex="-1"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="eye-invisible"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"
                    />
                    <path
                      d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class="ant-form-item-required"
          for="register_confirm"
          title="Confirm Password"
        >
          Confirm Password
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined ant-input-password"
            >
              <input
                aria-required="true"
                class="ant-input"
                id="register_confirm"
                type="password"
                value=""
              />
              <span
                class="ant-input-suffix"
              >
                <span
                  aria-label="eye-invisible"
                  class="anticon anticon-eye-invisible ant-input-password-icon"
                  role="img"
                  tabindex="-1"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="eye-invisible"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"
                    />
                    <path
                      d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"
                    />
                  </svg>
                </span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class="ant-form-item-required"
          for="register_nickname"
          title="Nickname"
        >
          Nickname
          <span
            aria-describedby="test-id"
            aria-label="question-circle"
            class="anticon anticon-question-circle ant-form-item-tooltip"
            role="img"
            title=""
          >
            <svg
              aria-hidden="true"
              data-icon="question-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
              <path
                d="M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0130.9-44.8c59-22.7 97.1-74.7 97.1-132.5.1-39.3-17.1-76-48.3-103.3zM472 732a40 40 0 1080 0 40 40 0 10-80 0z"
              />
            </svg>
          </span>
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              aria-required="true"
              class="ant-input ant-input-outlined"
              id="register_nickname"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class="ant-form-item-required"
          for="register_residence"
          title="Habitual Residence"
        >
          Habitual Residence
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              aria-required="true"
              class="ant-select ant-cascader ant-select-outlined ant-select-in-form-item ant-select-single ant-select-allow-clear ant-select-show-arrow"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="register_residence_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="register_residence_list"
                      aria-required="true"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      id="register_residence"
                      readonly=""
                      role="combobox"
                      style="opacity:0"
                      type="search"
                      unselectable="on"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-item"
                    title="Zhejiang / Hangzhou / West Lake"
                  >
                    Zhejiang / Hangzhou / West Lake
                  </span>
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
              </span>
              <span
                aria-hidden="true"
                class="ant-select-clear"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="close-circle"
                  class="anticon anticon-close-circle"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="close-circle"
                    fill="currentColor"
                    fill-rule="evenodd"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class="ant-form-item-required"
          for="register_phone"
          title="Phone Number"
        >
          Phone Number
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-group-wrapper ant-input-group-wrapper-outlined"
              style="width:100%"
            >
              <span
                class="ant-input-wrapper ant-input-group"
              >
                <span
                  class="ant-input-group-addon"
                >
                  <div
                    class="ant-select ant-select-outlined ant-select-single ant-select-show-arrow"
                    style="width:70px"
                  >
                    <div
                      class="ant-select-selector"
                    >
                      <span
                        class="ant-select-selection-wrap"
                      >
                        <span
                          class="ant-select-selection-search"
                        >
                          <input
                            aria-autocomplete="list"
                            aria-controls="register_prefix_list"
                            aria-expanded="false"
                            aria-haspopup="listbox"
                            aria-owns="register_prefix_list"
                            autocomplete="off"
                            class="ant-select-selection-search-input"
                            id="register_prefix"
                            readonly=""
                            role="combobox"
                            style="opacity:0"
                            type="search"
                            unselectable="on"
                            value=""
                          />
                        </span>
                        <span
                          class="ant-select-selection-item"
                          title="+86"
                        >
                          +86
                        </span>
                      </span>
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-arrow"
                      style="user-select:none;-webkit-user-select:none"
                      unselectable="on"
                    >
                      <span
                        aria-label="down"
                        class="anticon anticon-down ant-select-suffix"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="down"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                          />
                        </svg>
                      </span>
                    </span>
                  </div>
                </span>
                <input
                  aria-required="true"
                  class="ant-input ant-input-outlined"
                  id="register_phone"
                  type="text"
                  value=""
                />
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class="ant-form-item-required"
          for="register_donation"
          title="Donation"
        >
          Donation
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-input-number-group-wrapper ant-input-number-group-wrapper-outlined"
              style="width:100%"
            >
              <div
                class="ant-input-number-wrapper ant-input-number-group"
              >
                <div
                  class="ant-input-number ant-input-number-in-form-item ant-input-number-outlined"
                >
                  <div
                    class="ant-input-number-handler-wrap"
                  >
                    <span
                      aria-disabled="false"
                      aria-label="Increase Value"
                      class="ant-input-number-handler ant-input-number-handler-up"
                      role="button"
                      unselectable="on"
                    >
                      <span
                        aria-label="up"
                        class="anticon anticon-up ant-input-number-handler-up-inner"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="up"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                          />
                        </svg>
                      </span>
                    </span>
                    <span
                      aria-disabled="false"
                      aria-label="Decrease Value"
                      class="ant-input-number-handler ant-input-number-handler-down"
                      role="button"
                      unselectable="on"
                    >
                      <span
                        aria-label="down"
                        class="anticon anticon-down ant-input-number-handler-down-inner"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="down"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                          />
                        </svg>
                      </span>
                    </span>
                  </div>
                  <div
                    class="ant-input-number-input-wrap"
                  >
                    <input
                      aria-required="true"
                      autocomplete="off"
                      class="ant-input-number-input"
                      id="register_donation"
                      role="spinbutton"
                      step="1"
                      value=""
                    />
                  </div>
                </div>
                <div
                  class="ant-input-number-group-addon"
                >
                  <div
                    class="ant-select ant-select-outlined ant-select-single ant-select-show-arrow"
                    style="width:70px"
                  >
                    <div
                      class="ant-select-selector"
                    >
                      <span
                        class="ant-select-selection-wrap"
                      >
                        <span
                          class="ant-select-selection-search"
                        >
                          <input
                            aria-autocomplete="list"
                            aria-controls="register_suffix_list"
                            aria-expanded="false"
                            aria-haspopup="listbox"
                            aria-owns="register_suffix_list"
                            autocomplete="off"
                            class="ant-select-selection-search-input"
                            id="register_suffix"
                            readonly=""
                            role="combobox"
                            style="opacity:0"
                            type="search"
                            unselectable="on"
                            value=""
                          />
                        </span>
                        <span
                          class="ant-select-selection-placeholder"
                        />
                      </span>
                    </div>
                    <span
                      aria-hidden="true"
                      class="ant-select-arrow"
                      style="user-select:none;-webkit-user-select:none"
                      unselectable="on"
                    >
                      <span
                        aria-label="down"
                        class="anticon anticon-down ant-select-suffix"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="down"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                          />
                        </svg>
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class="ant-form-item-required"
          for="register_website"
          title="Website"
        >
          Website
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              aria-required="true"
              class="ant-select ant-select-outlined ant-select-in-form-item ant-select-auto-complete ant-select-single ant-select-customize-input ant-select-show-search"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="register_website_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="register_website_list"
                      aria-required="true"
                      autocomplete="off"
                      class="ant-input ant-input-outlined ant-select-selection-search-input"
                      id="register_website"
                      role="combobox"
                      type="search"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  >
                    website
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class="ant-form-item-required"
          for="register_intro"
          title="Intro"
        >
          Intro
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-textarea-affix-wrapper ant-input-textarea-show-count ant-input-show-count ant-input-outlined"
              data-count="0 / 100"
            >
              <textarea
                aria-required="true"
                class="ant-input"
                id="register_intro"
                maxlength="100"
              />
              <span
                class="ant-input-suffix"
              >
                <span
                  class="ant-input-data-count"
                >
                  0 / 100
                </span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class="ant-form-item-required"
          for="register_gender"
          title="Gender"
        >
          Gender
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              aria-required="true"
              class="ant-select ant-select-outlined ant-select-in-form-item ant-select-single ant-select-show-arrow"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="register_gender_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="register_gender_list"
                      aria-required="true"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      id="register_gender"
                      readonly=""
                      role="combobox"
                      style="opacity:0"
                      type="search"
                      unselectable="on"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  >
                    select your gender
                  </span>
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class=""
          title="Captcha"
        >
          Captcha
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-row"
              style="margin-left:-4px;margin-right:-4px"
            >
              <div
                class="ant-col ant-col-12"
                style="padding-left:4px;padding-right:4px"
              >
                <input
                  aria-required="true"
                  class="ant-input ant-input-outlined"
                  id="register_captcha"
                  type="text"
                  value=""
                />
              </div>
              <div
                class="ant-col ant-col-12"
                style="padding-left:4px;padding-right:4px"
              >
                <button
                  class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
                  type="button"
                >
                  <span>
                    Get captcha
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div
          class="ant-form-item-additional"
        >
          <div
            class="ant-form-item-extra"
          >
            We must make sure that your are a human.
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-xs-offset-0 ant-col-sm-16 ant-col-sm-offset-8"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <label
              class="ant-checkbox-wrapper ant-checkbox-wrapper-in-form-item"
            >
              <span
                class="ant-checkbox ant-wave-target"
              >
                <input
                  class="ant-checkbox-input"
                  id="register_agreement"
                  type="checkbox"
                />
                <span
                  class="ant-checkbox-inner"
                />
              </span>
              <span
                class="ant-checkbox-label"
              >
                I have read the 
                <a
                  href=""
                >
                  agreement
                </a>
              </span>
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-xs-offset-0 ant-col-sm-16 ant-col-sm-offset-8"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              type="submit"
            >
              <span>
                Register
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/required-mark.tsx correctly 1`] = `
<form
  class="ant-form ant-form-vertical"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class="ant-form-item-required-mark-optional"
          for="requiredMarkValue"
          title="Required Mark"
        >
          Required Mark
          <span
            class="ant-form-item-optional"
            title=""
          >
            (optional)
          </span>
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-radio-group ant-radio-group-outline"
              id="requiredMarkValue"
            >
              <label
                class="ant-radio-button-wrapper ant-radio-button-wrapper-in-form-item"
              >
                <span
                  class="ant-radio-button"
                >
                  <input
                    class="ant-radio-button-input"
                    name="requiredMarkValue"
                    type="radio"
                    value="true"
                  />
                  <span
                    class="ant-radio-button-inner"
                  />
                </span>
                <span
                  class="ant-radio-button-label"
                >
                  Default
                </span>
              </label>
              <label
                class="ant-radio-button-wrapper ant-radio-button-wrapper-checked ant-radio-button-wrapper-in-form-item"
              >
                <span
                  class="ant-radio-button ant-radio-button-checked"
                >
                  <input
                    checked=""
                    class="ant-radio-button-input"
                    name="requiredMarkValue"
                    type="radio"
                    value="optional"
                  />
                  <span
                    class="ant-radio-button-inner"
                  />
                </span>
                <span
                  class="ant-radio-button-label"
                >
                  Optional
                </span>
              </label>
              <label
                class="ant-radio-button-wrapper ant-radio-button-wrapper-in-form-item"
              >
                <span
                  class="ant-radio-button"
                >
                  <input
                    class="ant-radio-button-input"
                    name="requiredMarkValue"
                    type="radio"
                    value="false"
                  />
                  <span
                    class="ant-radio-button-inner"
                  />
                </span>
                <span
                  class="ant-radio-button-label"
                >
                  Hidden
                </span>
              </label>
              <label
                class="ant-radio-button-wrapper ant-radio-button-wrapper-in-form-item"
              >
                <span
                  class="ant-radio-button"
                >
                  <input
                    class="ant-radio-button-input"
                    name="requiredMarkValue"
                    type="radio"
                    value="customize"
                  />
                  <span
                    class="ant-radio-button-inner"
                  />
                </span>
                <span
                  class="ant-radio-button-label"
                >
                  Customize
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class="ant-form-item-required ant-form-item-required-mark-optional"
          title="Field A"
        >
          Field A
          <span
            aria-describedby="test-id"
            aria-label="question-circle"
            class="anticon anticon-question-circle ant-form-item-tooltip"
            role="img"
            title=""
          >
            <svg
              aria-hidden="true"
              data-icon="question-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
              <path
                d="M623.6 316.7C593.6 290.4 554 276 512 276s-81.6 14.5-111.6 40.7C369.2 344 352 380.7 352 420v7.6c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V420c0-44.1 43.1-80 96-80s96 35.9 96 80c0 31.1-22 59.6-56.1 72.7-21.2 8.1-39.2 22.3-52.1 40.9-13.1 19-19.9 41.8-19.9 64.9V620c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8v-22.7a48.3 48.3 0 0130.9-44.8c59-22.7 97.1-74.7 97.1-132.5.1-39.3-17.1-76-48.3-103.3zM472 732a40 40 0 1080 0 40 40 0 10-80 0z"
              />
            </svg>
          </span>
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              placeholder="input placeholder"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class="ant-form-item-required-mark-optional"
          title="Field B"
        >
          Field B
          <span
            aria-describedby="test-id"
            aria-label="info-circle"
            class="anticon anticon-info-circle ant-form-item-tooltip"
            role="img"
            title=""
          >
            <svg
              aria-hidden="true"
              data-icon="info-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
              <path
                d="M464 336a48 48 0 1096 0 48 48 0 10-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z"
              />
            </svg>
          </span>
          <span
            class="ant-form-item-optional"
            title=""
          >
            (optional)
          </span>
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              placeholder="input placeholder"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              type="button"
            >
              <span>
                Submit
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/size.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal ant-form-default"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-4 ant-form-item-label"
      >
        <label
          class=""
          for="size"
          title="Form Size"
        >
          Form Size
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-radio-group ant-radio-group-outline ant-radio-group-default"
              id="size"
            >
              <label
                class="ant-radio-button-wrapper ant-radio-button-wrapper-in-form-item"
              >
                <span
                  class="ant-radio-button"
                >
                  <input
                    class="ant-radio-button-input"
                    name="size"
                    type="radio"
                    value="small"
                  />
                  <span
                    class="ant-radio-button-inner"
                  />
                </span>
                <span
                  class="ant-radio-button-label"
                >
                  Small
                </span>
              </label>
              <label
                class="ant-radio-button-wrapper ant-radio-button-wrapper-checked ant-radio-button-wrapper-in-form-item"
              >
                <span
                  class="ant-radio-button ant-radio-button-checked"
                >
                  <input
                    checked=""
                    class="ant-radio-button-input"
                    name="size"
                    type="radio"
                    value="default"
                  />
                  <span
                    class="ant-radio-button-inner"
                  />
                </span>
                <span
                  class="ant-radio-button-label"
                >
                  Default
                </span>
              </label>
              <label
                class="ant-radio-button-wrapper ant-radio-button-wrapper-in-form-item"
              >
                <span
                  class="ant-radio-button"
                >
                  <input
                    class="ant-radio-button-input"
                    name="size"
                    type="radio"
                    value="large"
                  />
                  <span
                    class="ant-radio-button-inner"
                  />
                </span>
                <span
                  class="ant-radio-button-label"
                >
                  Large
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-4 ant-form-item-label"
      >
        <label
          class=""
          title="Input"
        >
          Input
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-4 ant-form-item-label"
      >
        <label
          class=""
          title="Select"
        >
          Select
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-select ant-select-outlined ant-select-in-form-item ant-select-single ant-select-show-arrow"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="undefined_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="undefined_list"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      readonly=""
                      role="combobox"
                      style="opacity:0"
                      type="search"
                      unselectable="on"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  />
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-4 ant-form-item-label"
      >
        <label
          class=""
          title="TreeSelect"
        >
          TreeSelect
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-select ant-tree-select ant-select-outlined ant-select-in-form-item ant-select-single ant-select-show-arrow"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="undefined_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="undefined_list"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      readonly=""
                      role="combobox"
                      style="opacity:0"
                      type="search"
                      unselectable="on"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  />
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-4 ant-form-item-label"
      >
        <label
          class=""
          title="Cascader"
        >
          Cascader
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-select ant-cascader ant-select-outlined ant-select-in-form-item ant-select-single ant-select-allow-clear ant-select-show-arrow"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="undefined_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="undefined_list"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      readonly=""
                      role="combobox"
                      style="opacity:0"
                      type="search"
                      unselectable="on"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  />
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-4 ant-form-item-label"
      >
        <label
          class=""
          title="DatePicker"
        >
          DatePicker
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-picker ant-picker-default ant-picker-outlined"
            >
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  autocomplete="off"
                  placeholder="Select date"
                  size="12"
                  value=""
                />
                <span
                  class="ant-picker-suffix"
                >
                  <span
                    aria-label="calendar"
                    class="anticon anticon-calendar"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="calendar"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-4 ant-form-item-label"
      >
        <label
          class=""
          title="InputNumber"
        >
          InputNumber
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-input-number ant-input-number-in-form-item ant-input-number-outlined"
            >
              <div
                class="ant-input-number-handler-wrap"
              >
                <span
                  aria-disabled="false"
                  aria-label="Increase Value"
                  class="ant-input-number-handler ant-input-number-handler-up"
                  role="button"
                  unselectable="on"
                >
                  <span
                    aria-label="up"
                    class="anticon anticon-up ant-input-number-handler-up-inner"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="up"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                      />
                    </svg>
                  </span>
                </span>
                <span
                  aria-disabled="false"
                  aria-label="Decrease Value"
                  class="ant-input-number-handler ant-input-number-handler-down"
                  role="button"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-input-number-handler-down-inner"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-input-number-input-wrap"
              >
                <input
                  autocomplete="off"
                  class="ant-input-number-input"
                  role="spinbutton"
                  step="1"
                  value=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-4 ant-form-item-label"
      >
        <label
          class=""
          title="Switch"
        >
          Switch
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              aria-checked="false"
              class="ant-switch"
              role="switch"
              type="button"
            >
              <div
                class="ant-switch-handle"
              />
              <span
                class="ant-switch-inner"
              >
                <span
                  class="ant-switch-inner-checked"
                />
                <span
                  class="ant-switch-inner-unchecked"
                />
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-4 ant-form-item-label"
      >
        <label
          class=""
          title="Button"
        >
          Button
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
              type="button"
            >
              <span>
                Button
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/time-related-controls.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  id="time_related_controls"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class="ant-form-item-required"
          for="time_related_controls_date-picker"
          title="DatePicker"
        >
          DatePicker
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-picker ant-picker-outlined"
            >
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  aria-required="true"
                  autocomplete="off"
                  id="time_related_controls_date-picker"
                  placeholder="Select date"
                  size="12"
                  value=""
                />
                <span
                  class="ant-picker-suffix"
                >
                  <span
                    aria-label="calendar"
                    class="anticon anticon-calendar"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="calendar"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class="ant-form-item-required"
          for="time_related_controls_date-time-picker"
          title="DatePicker[showTime]"
        >
          DatePicker[showTime]
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-picker ant-picker-outlined"
            >
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  aria-required="true"
                  autocomplete="off"
                  id="time_related_controls_date-time-picker"
                  placeholder="Select date"
                  size="21"
                  value=""
                />
                <span
                  class="ant-picker-suffix"
                >
                  <span
                    aria-label="calendar"
                    class="anticon anticon-calendar"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="calendar"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class="ant-form-item-required"
          for="time_related_controls_month-picker"
          title="MonthPicker"
        >
          MonthPicker
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-picker ant-picker-outlined"
            >
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  aria-required="true"
                  autocomplete="off"
                  id="time_related_controls_month-picker"
                  placeholder="Select month"
                  size="12"
                  value=""
                />
                <span
                  class="ant-picker-suffix"
                >
                  <span
                    aria-label="calendar"
                    class="anticon anticon-calendar"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="calendar"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class="ant-form-item-required"
          for="time_related_controls_range-picker"
          title="RangePicker"
        >
          RangePicker
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-picker ant-picker-range ant-picker-outlined"
            >
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  aria-required="true"
                  autocomplete="off"
                  date-range="start"
                  id="time_related_controls_range-picker"
                  placeholder="Start date"
                  size="12"
                  value=""
                />
              </div>
              <div
                class="ant-picker-range-separator"
              >
                <span
                  aria-label="to"
                  class="ant-picker-separator"
                >
                  <span
                    aria-label="swap-right"
                    class="anticon anticon-swap-right"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="swap-right"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                    >
                      <path
                        d="M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  aria-required="true"
                  autocomplete="off"
                  date-range="end"
                  placeholder="End date"
                  size="12"
                  value=""
                />
              </div>
              <div
                class="ant-picker-active-bar"
                style="position:absolute;width:0"
              />
              <span
                class="ant-picker-suffix"
              >
                <span
                  aria-label="calendar"
                  class="anticon anticon-calendar"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="calendar"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class="ant-form-item-required"
          for="time_related_controls_range-time-picker"
          title="RangePicker[showTime]"
        >
          RangePicker[showTime]
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-picker ant-picker-range ant-picker-outlined"
            >
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  aria-required="true"
                  autocomplete="off"
                  date-range="start"
                  id="time_related_controls_range-time-picker"
                  placeholder="Start date"
                  size="21"
                  value=""
                />
              </div>
              <div
                class="ant-picker-range-separator"
              >
                <span
                  aria-label="to"
                  class="ant-picker-separator"
                >
                  <span
                    aria-label="swap-right"
                    class="anticon anticon-swap-right"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="swap-right"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                    >
                      <path
                        d="M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  aria-required="true"
                  autocomplete="off"
                  date-range="end"
                  placeholder="End date"
                  size="21"
                  value=""
                />
              </div>
              <div
                class="ant-picker-active-bar"
                style="position:absolute;width:0"
              />
              <span
                class="ant-picker-suffix"
              >
                <span
                  aria-label="calendar"
                  class="anticon anticon-calendar"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="calendar"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-8"
      >
        <label
          class="ant-form-item-required"
          for="time_related_controls_time-picker"
          title="TimePicker"
        >
          TimePicker
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-picker ant-picker-outlined"
            >
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  aria-required="true"
                  autocomplete="off"
                  id="time_related_controls_time-picker"
                  placeholder="Select time"
                  size="10"
                  value=""
                />
                <span
                  class="ant-picker-suffix"
                >
                  <span
                    aria-label="clock-circle"
                    class="anticon anticon-clock-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="clock-circle"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
                      />
                      <path
                        d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-16 ant-col-sm-offset-8"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              type="submit"
            >
              <span>
                Submit
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/useWatch.tsx correctly 1`] = `
Array [
  <form
    autocomplete="off"
    class="ant-form ant-form-vertical"
  >
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-form-item-label"
        >
          <label
            class=""
            for="name"
            title="Name (Watch to trigger rerender)"
          >
            Name (Watch to trigger rerender)
          </label>
        </div>
        <div
          class="ant-col ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <input
                class="ant-input ant-input-outlined"
                id="name"
                type="text"
                value=""
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="ant-form-item"
    >
      <div
        class="ant-row ant-form-item-row"
      >
        <div
          class="ant-col ant-form-item-label"
        >
          <label
            class=""
            for="age"
            title="Age (Not Watch)"
          >
            Age (Not Watch)
          </label>
        </div>
        <div
          class="ant-col ant-form-item-control"
        >
          <div
            class="ant-form-item-control-input"
          >
            <div
              class="ant-form-item-control-input-content"
            >
              <div
                class="ant-input-number ant-input-number-in-form-item ant-input-number-outlined"
              >
                <div
                  class="ant-input-number-handler-wrap"
                >
                  <span
                    aria-disabled="false"
                    aria-label="Increase Value"
                    class="ant-input-number-handler ant-input-number-handler-up"
                    role="button"
                    unselectable="on"
                  >
                    <span
                      aria-label="up"
                      class="anticon anticon-up ant-input-number-handler-up-inner"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="up"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                        />
                      </svg>
                    </span>
                  </span>
                  <span
                    aria-disabled="false"
                    aria-label="Decrease Value"
                    class="ant-input-number-handler ant-input-number-handler-down"
                    role="button"
                    unselectable="on"
                  >
                    <span
                      aria-label="down"
                      class="anticon anticon-down ant-input-number-handler-down-inner"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="down"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                        />
                      </svg>
                    </span>
                  </span>
                </div>
                <div
                  class="ant-input-number-input-wrap"
                >
                  <input
                    autocomplete="off"
                    class="ant-input-number-input"
                    id="age"
                    role="spinbutton"
                    step="1"
                    value=""
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>,
  <article
    class="ant-typography"
  >
    <pre>
      Name Value: 
    </pre>
    <pre>
      Custom Value: 
    </pre>
  </article>,
]
`;

exports[`renders components/form/demo/validate-only.tsx correctly 1`] = `
<form
  autocomplete="off"
  class="ant-form ant-form-vertical"
  id="validateOnly"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="validateOnly_name"
          title="Name"
        >
          Name
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              aria-required="true"
              class="ant-input ant-input-outlined"
              id="validateOnly_name"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="validateOnly_age"
          title="Age"
        >
          Age
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              aria-required="true"
              class="ant-input ant-input-outlined"
              id="validateOnly_age"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small"
            >
              <div
                class="ant-space-item"
              >
                <button
                  class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                  disabled=""
                  type="submit"
                >
                  <span>
                    Submit
                  </span>
                </button>
              </div>
              <div
                class="ant-space-item"
              >
                <button
                  class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
                  type="reset"
                >
                  <span>
                    Reset
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/validate-other.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  id="validate_other"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class=""
          title="Plain Text"
        >
          Plain Text
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-form-text"
            >
              China
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="validate_other_select"
          title="Select"
        >
          Select
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              aria-required="true"
              class="ant-select ant-select-outlined ant-select-in-form-item ant-select-has-feedback ant-select-single ant-select-show-arrow"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="validate_other_select_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="validate_other_select_list"
                      aria-required="true"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      id="validate_other_select"
                      readonly=""
                      role="combobox"
                      style="opacity:0"
                      type="search"
                      unselectable="on"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  >
                    Please select a country
                  </span>
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="validate_other_select-multiple"
          title="Select[multiple]"
        >
          Select[multiple]
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              aria-required="true"
              class="ant-select ant-select-outlined ant-select-in-form-item ant-select-multiple ant-select-show-arrow ant-select-show-search"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <div
                    class="ant-select-selection-overflow"
                  >
                    <div
                      class="ant-select-selection-overflow-item ant-select-selection-overflow-item-suffix"
                      style="opacity:1"
                    >
                      <div
                        class="ant-select-selection-search"
                        style="width:0"
                      >
                        <input
                          aria-autocomplete="list"
                          aria-controls="validate_other_select-multiple_list"
                          aria-expanded="false"
                          aria-haspopup="listbox"
                          aria-owns="validate_other_select-multiple_list"
                          aria-required="true"
                          autocomplete="off"
                          class="ant-select-selection-search-input"
                          id="validate_other_select-multiple"
                          readonly=""
                          role="combobox"
                          style="opacity:0"
                          type="search"
                          unselectable="on"
                          value=""
                        />
                        <span
                          aria-hidden="true"
                          class="ant-select-selection-search-mirror"
                        >
                        </span>
                      </div>
                    </div>
                  </div>
                  <span
                    class="ant-select-selection-placeholder"
                  >
                    Please select favourite colors
                  </span>
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class=""
          title="InputNumber"
        >
          InputNumber
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-input-number ant-input-number-in-form-item ant-input-number-outlined"
            >
              <div
                class="ant-input-number-handler-wrap"
              >
                <span
                  aria-disabled="false"
                  aria-label="Increase Value"
                  class="ant-input-number-handler ant-input-number-handler-up"
                  role="button"
                  unselectable="on"
                >
                  <span
                    aria-label="up"
                    class="anticon anticon-up ant-input-number-handler-up-inner"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="up"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                      />
                    </svg>
                  </span>
                </span>
                <span
                  aria-disabled="false"
                  aria-label="Decrease Value"
                  class="ant-input-number-handler ant-input-number-handler-down"
                  role="button"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-input-number-handler-down-inner"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-input-number-input-wrap"
              >
                <input
                  aria-valuemax="10"
                  aria-valuemin="1"
                  aria-valuenow="3"
                  autocomplete="off"
                  class="ant-input-number-input"
                  id="validate_other_input-number"
                  role="spinbutton"
                  step="1"
                  value="3"
                />
              </div>
            </div>
            <span
              class="ant-form-text"
              style="margin-inline-start:8px"
            >
              machines
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class=""
          for="validate_other_switch"
          title="Switch"
        >
          Switch
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              aria-checked="false"
              class="ant-switch"
              id="validate_other_switch"
              role="switch"
              type="button"
            >
              <div
                class="ant-switch-handle"
              />
              <span
                class="ant-switch-inner"
              >
                <span
                  class="ant-switch-inner-checked"
                />
                <span
                  class="ant-switch-inner-unchecked"
                />
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class=""
          for="validate_other_slider"
          title="Slider"
        >
          Slider
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-slider ant-slider-horizontal ant-slider-with-marks"
              id="validate_other_slider"
            >
              <div
                class="ant-slider-rail"
              />
              <div
                class="ant-slider-track"
                style="left:0%;width:0%"
              />
              <div
                class="ant-slider-step"
              >
                <span
                  class="ant-slider-dot ant-slider-dot-active"
                  style="left:0%;transform:translateX(-50%)"
                />
                <span
                  class="ant-slider-dot"
                  style="left:20%;transform:translateX(-50%)"
                />
                <span
                  class="ant-slider-dot"
                  style="left:40%;transform:translateX(-50%)"
                />
                <span
                  class="ant-slider-dot"
                  style="left:60%;transform:translateX(-50%)"
                />
                <span
                  class="ant-slider-dot"
                  style="left:80%;transform:translateX(-50%)"
                />
                <span
                  class="ant-slider-dot"
                  style="left:100%;transform:translateX(-50%)"
                />
              </div>
              <div
                aria-describedby="test-id"
                aria-disabled="false"
                aria-orientation="horizontal"
                aria-valuemax="100"
                aria-valuemin="0"
                aria-valuenow="0"
                class="ant-slider-handle"
                role="slider"
                style="left:0%;transform:translateX(-50%)"
                tabindex="0"
              />
              <div
                class="ant-slider-mark"
              >
                <span
                  class="ant-slider-mark-text ant-slider-mark-text-active"
                  style="left:0%;transform:translateX(-50%)"
                >
                  A
                </span>
                <span
                  class="ant-slider-mark-text"
                  style="left:20%;transform:translateX(-50%)"
                >
                  B
                </span>
                <span
                  class="ant-slider-mark-text"
                  style="left:40%;transform:translateX(-50%)"
                >
                  C
                </span>
                <span
                  class="ant-slider-mark-text"
                  style="left:60%;transform:translateX(-50%)"
                >
                  D
                </span>
                <span
                  class="ant-slider-mark-text"
                  style="left:80%;transform:translateX(-50%)"
                >
                  E
                </span>
                <span
                  class="ant-slider-mark-text"
                  style="left:100%;transform:translateX(-50%)"
                >
                  F
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class=""
          for="validate_other_radio-group"
          title="Radio.Group"
        >
          Radio.Group
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-radio-group ant-radio-group-outline"
              id="validate_other_radio-group"
            >
              <label
                class="ant-radio-wrapper ant-radio-wrapper-in-form-item"
              >
                <span
                  class="ant-radio ant-wave-target"
                >
                  <input
                    class="ant-radio-input"
                    name="radio-group"
                    type="radio"
                    value="a"
                  />
                  <span
                    class="ant-radio-inner"
                  />
                </span>
                <span
                  class="ant-radio-label"
                >
                  item 1
                </span>
              </label>
              <label
                class="ant-radio-wrapper ant-radio-wrapper-in-form-item"
              >
                <span
                  class="ant-radio ant-wave-target"
                >
                  <input
                    class="ant-radio-input"
                    name="radio-group"
                    type="radio"
                    value="b"
                  />
                  <span
                    class="ant-radio-inner"
                  />
                </span>
                <span
                  class="ant-radio-label"
                >
                  item 2
                </span>
              </label>
              <label
                class="ant-radio-wrapper ant-radio-wrapper-in-form-item"
              >
                <span
                  class="ant-radio ant-wave-target"
                >
                  <input
                    class="ant-radio-input"
                    name="radio-group"
                    type="radio"
                    value="c"
                  />
                  <span
                    class="ant-radio-inner"
                  />
                </span>
                <span
                  class="ant-radio-label"
                >
                  item 3
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="validate_other_radio-button"
          title="Radio.Button"
        >
          Radio.Button
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              aria-required="true"
              class="ant-radio-group ant-radio-group-outline"
              id="validate_other_radio-button"
            >
              <label
                class="ant-radio-button-wrapper ant-radio-button-wrapper-in-form-item"
              >
                <span
                  class="ant-radio-button"
                >
                  <input
                    class="ant-radio-button-input"
                    name="radio-button"
                    type="radio"
                    value="a"
                  />
                  <span
                    class="ant-radio-button-inner"
                  />
                </span>
                <span
                  class="ant-radio-button-label"
                >
                  item 1
                </span>
              </label>
              <label
                class="ant-radio-button-wrapper ant-radio-button-wrapper-in-form-item"
              >
                <span
                  class="ant-radio-button"
                >
                  <input
                    class="ant-radio-button-input"
                    name="radio-button"
                    type="radio"
                    value="b"
                  />
                  <span
                    class="ant-radio-button-inner"
                  />
                </span>
                <span
                  class="ant-radio-button-label"
                >
                  item 2
                </span>
              </label>
              <label
                class="ant-radio-button-wrapper ant-radio-button-wrapper-in-form-item"
              >
                <span
                  class="ant-radio-button"
                >
                  <input
                    class="ant-radio-button-input"
                    name="radio-button"
                    type="radio"
                    value="c"
                  />
                  <span
                    class="ant-radio-button-inner"
                  />
                </span>
                <span
                  class="ant-radio-button-label"
                >
                  item 3
                </span>
              </label>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class=""
          for="validate_other_checkbox-group"
          title="Checkbox.Group"
        >
          Checkbox.Group
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-checkbox-group"
              id="validate_other_checkbox-group"
            >
              <div
                class="ant-row"
              >
                <div
                  class="ant-col ant-col-8"
                >
                  <label
                    class="ant-checkbox-wrapper ant-checkbox-wrapper-checked ant-checkbox-wrapper-in-form-item"
                    style="line-height:32px"
                  >
                    <span
                      class="ant-checkbox ant-wave-target ant-checkbox-checked"
                    >
                      <input
                        checked=""
                        class="ant-checkbox-input"
                        type="checkbox"
                        value="A"
                      />
                      <span
                        class="ant-checkbox-inner"
                      />
                    </span>
                    <span
                      class="ant-checkbox-label"
                    >
                      A
                    </span>
                  </label>
                </div>
                <div
                  class="ant-col ant-col-8"
                >
                  <label
                    class="ant-checkbox-wrapper ant-checkbox-wrapper-checked ant-checkbox-wrapper-disabled ant-checkbox-wrapper-in-form-item"
                    style="line-height:32px"
                  >
                    <span
                      class="ant-checkbox ant-wave-target ant-checkbox-checked ant-checkbox-disabled"
                    >
                      <input
                        checked=""
                        class="ant-checkbox-input"
                        disabled=""
                        type="checkbox"
                        value="B"
                      />
                      <span
                        class="ant-checkbox-inner"
                      />
                    </span>
                    <span
                      class="ant-checkbox-label"
                    >
                      B
                    </span>
                  </label>
                </div>
                <div
                  class="ant-col ant-col-8"
                >
                  <label
                    class="ant-checkbox-wrapper ant-checkbox-wrapper-in-form-item"
                    style="line-height:32px"
                  >
                    <span
                      class="ant-checkbox ant-wave-target"
                    >
                      <input
                        class="ant-checkbox-input"
                        type="checkbox"
                        value="C"
                      />
                      <span
                        class="ant-checkbox-inner"
                      />
                    </span>
                    <span
                      class="ant-checkbox-label"
                    >
                      C
                    </span>
                  </label>
                </div>
                <div
                  class="ant-col ant-col-8"
                >
                  <label
                    class="ant-checkbox-wrapper ant-checkbox-wrapper-in-form-item"
                    style="line-height:32px"
                  >
                    <span
                      class="ant-checkbox ant-wave-target"
                    >
                      <input
                        class="ant-checkbox-input"
                        type="checkbox"
                        value="D"
                      />
                      <span
                        class="ant-checkbox-inner"
                      />
                    </span>
                    <span
                      class="ant-checkbox-label"
                    >
                      D
                    </span>
                  </label>
                </div>
                <div
                  class="ant-col ant-col-8"
                >
                  <label
                    class="ant-checkbox-wrapper ant-checkbox-wrapper-in-form-item"
                    style="line-height:32px"
                  >
                    <span
                      class="ant-checkbox ant-wave-target"
                    >
                      <input
                        class="ant-checkbox-input"
                        type="checkbox"
                        value="E"
                      />
                      <span
                        class="ant-checkbox-inner"
                      />
                    </span>
                    <span
                      class="ant-checkbox-label"
                    >
                      E
                    </span>
                  </label>
                </div>
                <div
                  class="ant-col ant-col-8"
                >
                  <label
                    class="ant-checkbox-wrapper ant-checkbox-wrapper-in-form-item"
                    style="line-height:32px"
                  >
                    <span
                      class="ant-checkbox ant-wave-target"
                    >
                      <input
                        class="ant-checkbox-input"
                        type="checkbox"
                        value="F"
                      />
                      <span
                        class="ant-checkbox-inner"
                      />
                    </span>
                    <span
                      class="ant-checkbox-label"
                    >
                      F
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class=""
          for="validate_other_rate"
          title="Rate"
        >
          Rate
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <ul
              class="ant-rate"
              id="validate_other_rate"
              tabindex="0"
            >
              <li
                class="ant-rate-star ant-rate-star-full"
              >
                <div
                  aria-checked="true"
                  aria-posinset="1"
                  aria-setsize="5"
                  role="radio"
                  tabindex="0"
                >
                  <div
                    class="ant-rate-star-first"
                  >
                    <span
                      aria-label="star"
                      class="anticon anticon-star"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="star"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                        />
                      </svg>
                    </span>
                  </div>
                  <div
                    class="ant-rate-star-second"
                  >
                    <span
                      aria-label="star"
                      class="anticon anticon-star"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="star"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                        />
                      </svg>
                    </span>
                  </div>
                </div>
              </li>
              <li
                class="ant-rate-star ant-rate-star-full"
              >
                <div
                  aria-checked="true"
                  aria-posinset="2"
                  aria-setsize="5"
                  role="radio"
                  tabindex="0"
                >
                  <div
                    class="ant-rate-star-first"
                  >
                    <span
                      aria-label="star"
                      class="anticon anticon-star"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="star"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                        />
                      </svg>
                    </span>
                  </div>
                  <div
                    class="ant-rate-star-second"
                  >
                    <span
                      aria-label="star"
                      class="anticon anticon-star"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="star"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                        />
                      </svg>
                    </span>
                  </div>
                </div>
              </li>
              <li
                class="ant-rate-star ant-rate-star-full"
              >
                <div
                  aria-checked="true"
                  aria-posinset="3"
                  aria-setsize="5"
                  role="radio"
                  tabindex="0"
                >
                  <div
                    class="ant-rate-star-first"
                  >
                    <span
                      aria-label="star"
                      class="anticon anticon-star"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="star"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                        />
                      </svg>
                    </span>
                  </div>
                  <div
                    class="ant-rate-star-second"
                  >
                    <span
                      aria-label="star"
                      class="anticon anticon-star"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="star"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                        />
                      </svg>
                    </span>
                  </div>
                </div>
              </li>
              <li
                class="ant-rate-star ant-rate-star-zero"
              >
                <div
                  aria-checked="true"
                  aria-posinset="4"
                  aria-setsize="5"
                  role="radio"
                  tabindex="0"
                >
                  <div
                    class="ant-rate-star-first"
                  >
                    <span
                      aria-label="star"
                      class="anticon anticon-star"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="star"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                        />
                      </svg>
                    </span>
                  </div>
                  <div
                    class="ant-rate-star-second"
                  >
                    <span
                      aria-label="star"
                      class="anticon anticon-star"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="star"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                        />
                      </svg>
                    </span>
                  </div>
                </div>
              </li>
              <li
                class="ant-rate-star ant-rate-star-zero"
              >
                <div
                  aria-checked="false"
                  aria-posinset="5"
                  aria-setsize="5"
                  role="radio"
                  tabindex="0"
                >
                  <div
                    class="ant-rate-star-first"
                  >
                    <span
                      aria-label="star"
                      class="anticon anticon-star"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="star"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                        />
                      </svg>
                    </span>
                  </div>
                  <div
                    class="ant-rate-star-second"
                  >
                    <span
                      aria-label="star"
                      class="anticon anticon-star"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="star"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M908.1 353.1l-253.9-36.9L540.7 86.1c-3.1-6.3-8.2-11.4-14.5-14.5-15.8-7.8-35-1.3-42.9 14.5L369.8 316.2l-253.9 36.9c-7 1-13.4 4.3-18.3 9.3a32.05 32.05 0 00.6 45.3l183.7 179.1-43.4 252.9a31.95 31.95 0 0046.4 33.7L512 754l227.1 119.4c6.2 3.3 13.4 4.4 20.3 3.2 17.4-3 29.1-19.5 26.1-36.9l-43.4-252.9 183.7-179.1c5-4.9 8.3-11.3 9.3-18.3 2.7-17.5-9.5-33.7-27-36.3z"
                        />
                      </svg>
                    </span>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class=""
          for="validate_other_upload"
          title="Upload"
        >
          Upload
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-upload-wrapper"
            >
              <div
                class="ant-upload ant-upload-select"
              >
                <span
                  class="ant-upload"
                >
                  <input
                    accept=""
                    aria-describedby="validate_other_upload_extra"
                    id="validate_other_upload"
                    name="logo"
                    style="display:none"
                    type="file"
                  />
                  <button
                    class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
                    type="button"
                  >
                    <span
                      class="ant-btn-icon"
                    >
                      <span
                        aria-label="upload"
                        class="anticon anticon-upload"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="upload"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"
                          />
                        </svg>
                      </span>
                    </span>
                    <span>
                      Click to upload
                    </span>
                  </button>
                </span>
              </div>
              <div
                class="ant-upload-list ant-upload-list-picture"
              />
            </span>
          </div>
        </div>
        <div
          class="ant-form-item-additional"
        >
          <div
            class="ant-form-item-extra"
            id="validate_other_upload_extra"
          >
            longgggggggggggggggggggggggggggggggggg
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class=""
          title="Dragger"
        >
          Dragger
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-upload-wrapper"
            >
              <div
                class="ant-upload ant-upload-drag"
              >
                <span
                  class="ant-upload ant-upload-btn"
                  role="button"
                  tabindex="0"
                >
                  <input
                    accept=""
                    id="validate_other_dragger"
                    name="files"
                    style="display:none"
                    type="file"
                  />
                  <div
                    class="ant-upload-drag-container"
                  >
                    <p
                      class="ant-upload-drag-icon"
                    >
                      <span
                        aria-label="inbox"
                        class="anticon anticon-inbox"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="inbox"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="0 0 1024 1024"
                          width="1em"
                        >
                          <path
                            d="M885.2 446.3l-.2-.8-112.2-285.1c-5-16.1-19.9-27.2-36.8-27.2H281.2c-17 0-32.1 11.3-36.9 27.6L139.4 443l-.3.7-.2.8c-1.3 4.9-1.7 9.9-1 14.8-.1 1.6-.2 3.2-.2 4.8V830a60.9 60.9 0 0060.8 60.8h627.2c33.5 0 60.8-27.3 60.9-60.8V464.1c0-1.3 0-2.6-.1-3.7.4-4.9 0-9.6-1.3-14.1zm-295.8-43l-.3 15.7c-.8 44.9-31.8 75.1-77.1 75.1-22.1 0-41.1-7.1-54.8-20.6S436 441.2 435.6 419l-.3-15.7H229.5L309 210h399.2l81.7 193.3H589.4zm-375 76.8h157.3c24.3 57.1 76 90.8 140.4 90.8 33.7 0 65-9.4 90.3-27.2 22.2-15.6 39.5-37.4 50.7-63.6h156.5V814H214.4V480.1z"
                          />
                        </svg>
                      </span>
                    </p>
                    <p
                      class="ant-upload-text"
                    >
                      Click or drag file to this area to upload
                    </p>
                    <p
                      class="ant-upload-hint"
                    >
                      Support for a single or bulk upload.
                    </p>
                  </div>
                </span>
              </div>
              <div
                class="ant-upload-list ant-upload-list-text"
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="validate_other_color-picker"
          title="ColorPicker"
        >
          ColorPicker
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              aria-describedby="test-id"
              aria-required="true"
              class="ant-color-picker-trigger"
              id="validate_other_color-picker"
            >
              <div
                class="ant-color-picker-clear"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-12 ant-col-offset-6 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small"
            >
              <div
                class="ant-space-item"
              >
                <button
                  class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                  type="submit"
                >
                  <span>
                    Submit
                  </span>
                </button>
              </div>
              <div
                class="ant-space-item"
              >
                <button
                  class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
                  type="reset"
                >
                  <span>
                    reset
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/validate-scroll-to-field.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  style="padding-block:32px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-offset-6 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
              type="button"
            >
              <span>
                Scroll to Bio
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="username"
          title="UserName"
        >
          UserName
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              aria-required="true"
              class="ant-input ant-input-outlined"
              id="username"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class=""
          for="occupation"
          title="Occupation"
        >
          Occupation
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-select ant-select-outlined ant-select-in-form-item ant-select-single ant-select-show-arrow"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="occupation_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="occupation_list"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      id="occupation"
                      readonly=""
                      role="combobox"
                      style="opacity:0"
                      type="search"
                      unselectable="on"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  />
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class=""
          for="motto"
          title="Motto"
        >
          Motto
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <textarea
              class="ant-input ant-input-outlined"
              id="motto"
              rows="4"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-6 ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="bio"
          title="Bio"
        >
          Bio
        </label>
      </div>
      <div
        class="ant-col ant-col-14 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <textarea
              aria-required="true"
              class="ant-input ant-input-outlined"
              id="bio"
              rows="6"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-offset-6 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-flex ant-flex-gap-small"
            >
              <button
                class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                type="submit"
              >
                <span>
                  Submit
                </span>
              </button>
              <button
                class="ant-btn ant-btn-default ant-btn-dangerous ant-btn-color-dangerous ant-btn-variant-outlined"
                type="button"
              >
                <span>
                  Reset
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/validate-static.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  style="max-width:600px"
>
  <div
    class="ant-form-item ant-form-item-with-help ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Fail"
        >
          Fail
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              class="ant-input ant-input-outlined ant-input-status-error"
              id="error"
              placeholder="unavailable choice"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-has-warning"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Warning"
        >
          Warning
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined ant-input-status-warning"
            >
              <span
                class="ant-input-prefix"
              >
                <span
                  aria-label="smile"
                  class="anticon anticon-smile"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="smile"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M288 421a48 48 0 1096 0 48 48 0 10-96 0zm352 0a48 48 0 1096 0 48 48 0 10-96 0zM512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm263 711c-34.2 34.2-74 61-118.3 79.8C611 874.2 562.3 884 512 884c-50.3 0-99-9.8-144.8-29.2A370.4 370.4 0 01248.9 775c-34.2-34.2-61-74-79.8-118.3C149.8 611 140 562.3 140 512s9.8-99 29.2-144.8A370.4 370.4 0 01249 248.9c34.2-34.2 74-61 118.3-79.8C413 149.8 461.7 140 512 140c50.3 0 99 9.8 144.8 29.2A370.4 370.4 0 01775.1 249c34.2 34.2 61 74 79.8 118.3C874.2 413 884 461.7 884 512s-9.8 99-29.2 144.8A368.89 368.89 0 01775 775zM664 533h-48.1c-4.2 0-7.8 3.2-8.1 7.4C604 589.9 562.5 629 512 629s-92.1-39.1-95.8-88.6c-.3-4.2-3.9-7.4-8.1-7.4H360a8 8 0 00-8 8.4c4.4 84.3 74.5 151.6 160 151.6s155.6-67.3 160-151.6a8 8 0 00-8-8.4z"
                    />
                  </svg>
                </span>
              </span>
              <input
                class="ant-input"
                id="warning"
                placeholder="Warning"
                type="text"
                value=""
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-with-help ant-form-item-has-feedback ant-form-item-is-validating"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Validating"
        >
          Validating
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined ant-input-status-validating"
            >
              <input
                class="ant-input"
                id="validating"
                placeholder="I'm the content is being validated"
                type="text"
                value=""
              />
              <span
                class="ant-input-suffix"
              >
                <span
                  class="ant-form-item-feedback-icon ant-form-item-feedback-icon-validating"
                >
                  <span
                    aria-label="loading"
                    class="anticon anticon-loading anticon-spin"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="loading"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                    >
                      <path
                        d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"
                      />
                    </svg>
                  </span>
                </span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-has-feedback ant-form-item-has-success"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Success"
        >
          Success
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined ant-input-status-success"
            >
              <input
                class="ant-input"
                id="success"
                placeholder="I'm the content"
                type="text"
                value=""
              />
              <span
                class="ant-input-suffix"
              >
                <span
                  class="ant-form-item-feedback-icon ant-form-item-feedback-icon-success"
                >
                  <span
                    aria-label="check-circle"
                    class="anticon anticon-check-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="check-circle"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-has-feedback ant-form-item-has-warning"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Warning"
        >
          Warning
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined ant-input-status-warning"
            >
              <input
                class="ant-input"
                id="warning2"
                placeholder="Warning"
                type="text"
                value=""
              />
              <span
                class="ant-input-suffix"
              >
                <span
                  class="ant-form-item-feedback-icon ant-form-item-feedback-icon-warning"
                >
                  <span
                    aria-label="exclamation-circle"
                    class="anticon anticon-exclamation-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="exclamation-circle"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
                      />
                    </svg>
                  </span>
                </span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-with-help ant-form-item-has-feedback ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Fail"
        >
          Fail
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined ant-input-status-error"
            >
              <input
                class="ant-input"
                id="error2"
                placeholder="unavailable choice"
                type="text"
                value=""
              />
              <span
                class="ant-input-suffix"
              >
                <span
                  class="ant-form-item-feedback-icon ant-form-item-feedback-icon-error"
                >
                  <span
                    aria-label="close-circle"
                    class="anticon anticon-close-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="close-circle"
                      fill="currentColor"
                      fill-rule="evenodd"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                      />
                    </svg>
                  </span>
                </span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-has-feedback ant-form-item-has-success"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Success"
        >
          Success
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-picker ant-picker-outlined ant-picker-status-success ant-picker-has-feedback"
              style="width:100%"
            >
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  autocomplete="off"
                  placeholder="Select date"
                  size="12"
                  value=""
                />
                <span
                  class="ant-picker-suffix"
                >
                  <span
                    aria-label="calendar"
                    class="anticon anticon-calendar"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="calendar"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
                      />
                    </svg>
                  </span>
                  <span
                    class="ant-form-item-feedback-icon ant-form-item-feedback-icon-success"
                  >
                    <span
                      aria-label="check-circle"
                      class="anticon anticon-check-circle"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="check-circle"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"
                        />
                      </svg>
                    </span>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-has-feedback ant-form-item-has-warning"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Warning"
        >
          Warning
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-picker ant-picker-outlined ant-picker-status-warning ant-picker-has-feedback"
              style="width:100%"
            >
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  autocomplete="off"
                  placeholder="Select time"
                  size="10"
                  value=""
                />
                <span
                  class="ant-picker-suffix"
                >
                  <span
                    aria-label="clock-circle"
                    class="anticon anticon-clock-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="clock-circle"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
                      />
                      <path
                        d="M686.7 638.6L544.1 535.5V288c0-4.4-3.6-8-8-8H488c-4.4 0-8 3.6-8 8v275.4c0 2.6 1.2 5 3.3 6.5l165.4 120.6c3.6 2.6 8.6 1.8 11.2-1.7l28.6-39c2.6-3.7 1.8-8.7-1.8-11.2z"
                      />
                    </svg>
                  </span>
                  <span
                    class="ant-form-item-feedback-icon ant-form-item-feedback-icon-warning"
                  >
                    <span
                      aria-label="exclamation-circle"
                      class="anticon anticon-exclamation-circle"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="exclamation-circle"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
                        />
                      </svg>
                    </span>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-has-feedback ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Error"
        >
          Error
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-picker ant-picker-range ant-picker-outlined ant-picker-status-error ant-picker-has-feedback"
              style="width:100%"
            >
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  autocomplete="off"
                  date-range="start"
                  placeholder="Start date"
                  size="12"
                  value=""
                />
              </div>
              <div
                class="ant-picker-range-separator"
              >
                <span
                  aria-label="to"
                  class="ant-picker-separator"
                >
                  <span
                    aria-label="swap-right"
                    class="anticon anticon-swap-right"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="swap-right"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                    >
                      <path
                        d="M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  autocomplete="off"
                  date-range="end"
                  placeholder="End date"
                  size="12"
                  value=""
                />
              </div>
              <div
                class="ant-picker-active-bar"
                style="position:absolute;width:0"
              />
              <span
                class="ant-picker-suffix"
              >
                <span
                  aria-label="calendar"
                  class="anticon anticon-calendar"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="calendar"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
                    />
                  </svg>
                </span>
                <span
                  class="ant-form-item-feedback-icon ant-form-item-feedback-icon-error"
                >
                  <span
                    aria-label="close-circle"
                    class="anticon anticon-close-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="close-circle"
                      fill="currentColor"
                      fill-rule="evenodd"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                      />
                    </svg>
                  </span>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-has-feedback ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Error"
        >
          Error
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-select ant-select-outlined ant-select-in-form-item ant-select-status-error ant-select-has-feedback ant-select-single ant-select-allow-clear ant-select-show-arrow"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="undefined_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="undefined_list"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      readonly=""
                      role="combobox"
                      style="opacity:0"
                      type="search"
                      unselectable="on"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  >
                    I'm Select
                  </span>
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
                <span
                  class="ant-form-item-feedback-icon ant-form-item-feedback-icon-error"
                >
                  <span
                    aria-label="close-circle"
                    class="anticon anticon-close-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="close-circle"
                      fill="currentColor"
                      fill-rule="evenodd"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                      />
                    </svg>
                  </span>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-with-help ant-form-item-has-feedback ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Validating"
        >
          Validating
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-select ant-cascader ant-select-outlined ant-select-in-form-item ant-select-status-error ant-select-has-feedback ant-select-single ant-select-allow-clear ant-select-show-arrow"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="undefined_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="undefined_list"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      readonly=""
                      role="combobox"
                      style="opacity:0"
                      type="search"
                      unselectable="on"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  >
                    I'm Cascader
                  </span>
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
                <span
                  class="ant-form-item-feedback-icon ant-form-item-feedback-icon-error"
                >
                  <span
                    aria-label="close-circle"
                    class="anticon anticon-close-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="close-circle"
                      fill="currentColor"
                      fill-rule="evenodd"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                      />
                    </svg>
                  </span>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-with-help ant-form-item-has-feedback ant-form-item-has-warning"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Warning"
        >
          Warning
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-select ant-tree-select ant-select-outlined ant-select-in-form-item ant-select-status-warning ant-select-has-feedback ant-select-single ant-select-allow-clear ant-select-show-arrow"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="undefined_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="undefined_list"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      readonly=""
                      role="combobox"
                      style="opacity:0"
                      type="search"
                      unselectable="on"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  >
                    I'm TreeSelect
                  </span>
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
                <span
                  class="ant-form-item-feedback-icon ant-form-item-feedback-icon-warning"
                >
                  <span
                    aria-label="exclamation-circle"
                    class="anticon anticon-exclamation-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="exclamation-circle"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
                      />
                    </svg>
                  </span>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
    style="margin-bottom:0"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="inline"
        >
          inline
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-form-item ant-form-item-with-help ant-form-item-has-error"
              style="display:inline-block;width:calc(50% - 12px)"
            >
              <div
                class="ant-row ant-form-item-row"
              >
                <div
                  class="ant-col ant-form-item-control"
                >
                  <div
                    class="ant-form-item-control-input"
                  >
                    <div
                      class="ant-form-item-control-input-content"
                    >
                      <div
                        class="ant-picker ant-picker-outlined ant-picker-status-error"
                      >
                        <div
                          class="ant-picker-input"
                        >
                          <input
                            aria-invalid="false"
                            autocomplete="off"
                            placeholder="Select date"
                            size="12"
                            value=""
                          />
                          <span
                            class="ant-picker-suffix"
                          >
                            <span
                              aria-label="calendar"
                              class="anticon anticon-calendar"
                              role="img"
                            >
                              <svg
                                aria-hidden="true"
                                data-icon="calendar"
                                fill="currentColor"
                                focusable="false"
                                height="1em"
                                viewBox="64 64 896 896"
                                width="1em"
                              >
                                <path
                                  d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
                                />
                              </svg>
                            </span>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <span
              style="display:inline-block;width:24px;line-height:32px;text-align:center"
            >
              -
            </span>
            <div
              class="ant-form-item"
              style="display:inline-block;width:calc(50% - 12px)"
            >
              <div
                class="ant-row ant-form-item-row"
              >
                <div
                  class="ant-col ant-form-item-control"
                >
                  <div
                    class="ant-form-item-control-input"
                  >
                    <div
                      class="ant-form-item-control-input-content"
                    >
                      <div
                        class="ant-picker ant-picker-outlined"
                      >
                        <div
                          class="ant-picker-input"
                        >
                          <input
                            aria-invalid="false"
                            autocomplete="off"
                            placeholder="Select date"
                            size="12"
                            value=""
                          />
                          <span
                            class="ant-picker-suffix"
                          >
                            <span
                              aria-label="calendar"
                              class="anticon anticon-calendar"
                              role="img"
                            >
                              <svg
                                aria-hidden="true"
                                data-icon="calendar"
                                fill="currentColor"
                                focusable="false"
                                height="1em"
                                viewBox="64 64 896 896"
                                width="1em"
                              >
                                <path
                                  d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
                                />
                              </svg>
                            </span>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-has-feedback ant-form-item-has-success"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Success"
        >
          Success
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-input-number-affix-wrapper ant-input-number-outlined ant-input-number-status-success ant-input-number-has-feedback"
              style="width:100%"
            >
              <div
                class="ant-input-number ant-input-number-in-form-item"
              >
                <div
                  class="ant-input-number-handler-wrap"
                >
                  <span
                    aria-disabled="false"
                    aria-label="Increase Value"
                    class="ant-input-number-handler ant-input-number-handler-up"
                    role="button"
                    unselectable="on"
                  >
                    <span
                      aria-label="up"
                      class="anticon anticon-up ant-input-number-handler-up-inner"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="up"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                        />
                      </svg>
                    </span>
                  </span>
                  <span
                    aria-disabled="false"
                    aria-label="Decrease Value"
                    class="ant-input-number-handler ant-input-number-handler-down"
                    role="button"
                    unselectable="on"
                  >
                    <span
                      aria-label="down"
                      class="anticon anticon-down ant-input-number-handler-down-inner"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="down"
                        fill="currentColor"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                        />
                      </svg>
                    </span>
                  </span>
                </div>
                <div
                  class="ant-input-number-input-wrap"
                >
                  <input
                    autocomplete="off"
                    class="ant-input-number-input"
                    role="spinbutton"
                    step="1"
                    value=""
                  />
                </div>
              </div>
              <span
                class="ant-input-number-suffix"
              >
                <span
                  class="ant-form-item-feedback-icon ant-form-item-feedback-icon-success"
                >
                  <span
                    aria-label="check-circle"
                    class="anticon anticon-check-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="check-circle"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-has-feedback ant-form-item-has-success"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Success"
        >
          Success
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined ant-input-status-success"
            >
              <input
                class="ant-input"
                placeholder="with allowClear"
                type="text"
                value=""
              />
              <span
                class="ant-input-suffix"
              >
                <button
                  class="ant-input-clear-icon ant-input-clear-icon-hidden ant-input-clear-icon-has-suffix"
                  tabindex="-1"
                  type="button"
                >
                  <span
                    aria-label="close-circle"
                    class="anticon anticon-close-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="close-circle"
                      fill="currentColor"
                      fill-rule="evenodd"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                      />
                    </svg>
                  </span>
                </button>
                <span
                  class="ant-form-item-feedback-icon ant-form-item-feedback-icon-success"
                >
                  <span
                    aria-label="check-circle"
                    class="anticon anticon-check-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="check-circle"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-has-feedback ant-form-item-has-warning"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Warning"
        >
          Warning
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined ant-input-status-warning ant-input-password"
            >
              <input
                class="ant-input"
                placeholder="with input password"
                type="password"
                value=""
              />
              <span
                class="ant-input-suffix"
              >
                <span
                  aria-label="eye-invisible"
                  class="anticon anticon-eye-invisible ant-input-password-icon"
                  role="img"
                  tabindex="-1"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="eye-invisible"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"
                    />
                    <path
                      d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"
                    />
                  </svg>
                </span>
                <span
                  class="ant-form-item-feedback-icon ant-form-item-feedback-icon-warning"
                >
                  <span
                    aria-label="exclamation-circle"
                    class="anticon anticon-exclamation-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="exclamation-circle"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
                      />
                    </svg>
                  </span>
                </span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-has-feedback ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Error"
        >
          Error
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined ant-input-status-error ant-input-password"
            >
              <input
                class="ant-input"
                placeholder="with input password and allowClear"
                type="password"
                value=""
              />
              <span
                class="ant-input-suffix"
              >
                <button
                  class="ant-input-clear-icon ant-input-clear-icon-hidden ant-input-clear-icon-has-suffix"
                  tabindex="-1"
                  type="button"
                >
                  <span
                    aria-label="close-circle"
                    class="anticon anticon-close-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="close-circle"
                      fill="currentColor"
                      fill-rule="evenodd"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                      />
                    </svg>
                  </span>
                </button>
                <span
                  aria-label="eye-invisible"
                  class="anticon anticon-eye-invisible ant-input-password-icon"
                  role="img"
                  tabindex="-1"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="eye-invisible"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M942.2 486.2Q889.47 375.11 816.7 305l-50.88 50.88C807.31 395.53 843.45 447.4 874.7 512 791.5 684.2 673.4 766 512 766q-72.67 0-133.87-22.38L323 798.75Q408 838 512 838q288.3 0 430.2-300.3a60.29 60.29 0 000-51.5zm-63.57-320.64L836 122.88a8 8 0 00-11.32 0L715.31 232.2Q624.86 186 512 186q-288.3 0-430.2 300.3a60.3 60.3 0 000 51.5q56.69 119.4 136.5 191.41L112.48 835a8 8 0 000 11.31L155.17 889a8 8 0 0011.31 0l712.15-712.12a8 8 0 000-11.32zM149.3 512C232.6 339.8 350.7 258 512 258c54.54 0 104.13 9.36 149.12 28.39l-70.3 70.3a176 176 0 00-238.13 238.13l-83.42 83.42C223.1 637.49 183.3 582.28 149.3 512zm246.7 0a112.11 112.11 0 01146.2-106.69L401.31 546.2A112 112 0 01396 512z"
                    />
                    <path
                      d="M508 624c-3.46 0-6.87-.16-10.25-.47l-52.82 52.82a176.09 176.09 0 00227.42-227.42l-52.82 52.82c.31 3.38.47 6.79.47 10.25a111.94 111.94 0 01-112 112z"
                    />
                  </svg>
                </span>
                <span
                  class="ant-form-item-feedback-icon ant-form-item-feedback-icon-error"
                >
                  <span
                    aria-label="close-circle"
                    class="anticon anticon-close-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="close-circle"
                      fill="currentColor"
                      fill-rule="evenodd"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                      />
                    </svg>
                  </span>
                </span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-has-feedback ant-form-item-has-success"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Success"
        >
          Success
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-otp"
              role="group"
            >
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 1"
                  class="ant-input ant-input-outlined ant-input-status-success ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 2"
                  class="ant-input ant-input-outlined ant-input-status-success ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 3"
                  class="ant-input ant-input-outlined ant-input-status-success ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 4"
                  class="ant-input ant-input-outlined ant-input-status-success ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 5"
                  class="ant-input ant-input-outlined ant-input-status-success ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 6"
                  class="ant-input ant-input-outlined ant-input-status-success ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-has-feedback ant-form-item-has-warning"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Warning"
        >
          Warning
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-otp"
              role="group"
            >
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 1"
                  class="ant-input ant-input-outlined ant-input-status-warning ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 2"
                  class="ant-input ant-input-outlined ant-input-status-warning ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 3"
                  class="ant-input ant-input-outlined ant-input-status-warning ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 4"
                  class="ant-input ant-input-outlined ant-input-status-warning ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 5"
                  class="ant-input ant-input-outlined ant-input-status-warning ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 6"
                  class="ant-input ant-input-outlined ant-input-status-warning ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-has-feedback ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Error"
        >
          Error
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-otp"
              role="group"
            >
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 1"
                  class="ant-input ant-input-outlined ant-input-status-error ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 2"
                  class="ant-input ant-input-outlined ant-input-status-error ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 3"
                  class="ant-input ant-input-outlined ant-input-status-error ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 4"
                  class="ant-input ant-input-outlined ant-input-status-error ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 5"
                  class="ant-input ant-input-outlined ant-input-status-error ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
              <span
                class="ant-otp-input-wrapper"
                role="presentation"
              >
                <input
                  aria-label="OTP Input 6"
                  class="ant-input ant-input-outlined ant-input-status-error ant-otp-input"
                  size="1"
                  type="text"
                  value=""
                />
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-has-feedback ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Fail"
        >
          Fail
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-mentions-affix-wrapper ant-mentions-outlined ant-mentions-status-error"
            >
              <div
                class="ant-mentions"
              >
                <textarea
                  class="rc-textarea"
                  rows="1"
                />
              </div>
              <span
                class="ant-mentions-suffix"
              >
                <span
                  class="ant-form-item-feedback-icon ant-form-item-feedback-icon-error"
                >
                  <span
                    aria-label="close-circle"
                    class="anticon anticon-close-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="close-circle"
                      fill="currentColor"
                      fill-rule="evenodd"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                      />
                    </svg>
                  </span>
                </span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item ant-form-item-with-help ant-form-item-has-feedback ant-form-item-has-error"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          title="Fail"
        >
          Fail
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-textarea-affix-wrapper ant-input-textarea-show-count ant-input-show-count ant-input-textarea-allow-clear ant-input-outlined ant-input-status-error"
              data-count="0"
            >
              <textarea
                class="ant-input"
              />
              <span
                class="ant-input-suffix"
              >
                <button
                  class="ant-input-clear-icon ant-input-clear-icon-hidden ant-input-clear-icon-has-suffix"
                  tabindex="-1"
                  type="button"
                >
                  <span
                    aria-label="close-circle"
                    class="anticon anticon-close-circle"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="close-circle"
                      fill="currentColor"
                      fill-rule="evenodd"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                      />
                    </svg>
                  </span>
                </button>
                <span
                  class="ant-input-textarea-suffix"
                >
                  <span
                    class="ant-form-item-feedback-icon ant-form-item-feedback-icon-error"
                  >
                    <span
                      aria-label="close-circle"
                      class="anticon anticon-close-circle"
                      role="img"
                    >
                      <svg
                        aria-hidden="true"
                        data-icon="close-circle"
                        fill="currentColor"
                        fill-rule="evenodd"
                        focusable="false"
                        height="1em"
                        viewBox="64 64 896 896"
                        width="1em"
                      >
                        <path
                          d="M512 64c247.4 0 448 200.6 448 448S759.4 960 512 960 64 759.4 64 512 264.6 64 512 64zm127.98 274.82h-.04l-.08.06L512 466.75 384.14 338.88c-.04-.05-.06-.06-.08-.06a.12.12 0 00-.07 0c-.03 0-.05.01-.09.05l-45.02 45.02a.2.2 0 00-.05.09.12.12 0 000 .07v.02a.27.27 0 00.06.06L466.75 512 338.88 639.86c-.05.04-.06.06-.06.08a.12.12 0 000 .07c0 .03.01.05.05.09l45.02 45.02a.2.2 0 00.09.05.12.12 0 00.07 0c.02 0 .04-.01.08-.05L512 557.25l127.86 127.87c.04.04.06.05.08.05a.12.12 0 00.07 0c.03 0 .05-.01.09-.05l45.02-45.02a.2.2 0 00.05-.09.12.12 0 000-.07v-.02a.27.27 0 00-.05-.06L557.25 512l127.87-127.86c.04-.04.05-.06.05-.08a.12.12 0 000-.07c0-.03-.01-.05-.05-.09l-45.02-45.02a.2.2 0 00-.09-.05.12.12 0 00-.07 0z"
                        />
                      </svg>
                    </span>
                  </span>
                </span>
                <span
                  class="ant-input-data-count"
                >
                  0
                </span>
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/validate-trigger.tsx correctly 1`] = `
<form
  autocomplete="off"
  class="ant-form ant-form-vertical"
  id="trigger"
  style="max-width:600px"
>
  <div
    class="ant-alert ant-alert-info ant-alert-no-icon"
    data-show="true"
    role="alert"
  >
    <div
      class="ant-alert-content"
    >
      <div
        class="ant-alert-message"
      >
        Use 'max' rule, continue type chars to see it
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          for="trigger_field_a"
          title="Field A"
        >
          Field A
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined"
            >
              <input
                class="ant-input"
                id="trigger_field_a"
                placeholder="Validate required onBlur"
                type="text"
                value=""
              />
              <span
                class="ant-input-suffix"
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          for="trigger_field_b"
          title="Field B"
        >
          Field B
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined"
            >
              <input
                class="ant-input"
                id="trigger_field_b"
                placeholder="Validate required debounce after 1s"
                type="text"
                value=""
              />
              <span
                class="ant-input-suffix"
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class=""
          for="trigger_field_c"
          title="Field C"
        >
          Field C
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <span
              class="ant-input-affix-wrapper ant-input-outlined"
            >
              <input
                class="ant-input"
                id="trigger_field_c"
                placeholder="Validate one by one"
                type="text"
                value=""
              />
              <span
                class="ant-input-suffix"
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/variant.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  style="max-width:600px"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class=""
          for="variant"
          title="Form variant"
        >
          Form variant
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              aria-label="segmented control"
              class="ant-segmented"
              id="variant"
              role="radiogroup"
              tabindex="0"
            >
              <div
                class="ant-segmented-group"
              >
                <label
                  class="ant-segmented-item"
                >
                  <input
                    class="ant-segmented-item-input"
                    name="test-id"
                    type="radio"
                  />
                  <div
                    aria-selected="false"
                    class="ant-segmented-item-label"
                    title="outlined"
                  >
                    outlined
                  </div>
                </label>
                <label
                  class="ant-segmented-item ant-segmented-item-selected"
                >
                  <input
                    checked=""
                    class="ant-segmented-item-input"
                    name="test-id"
                    type="radio"
                  />
                  <div
                    aria-selected="true"
                    class="ant-segmented-item-label"
                    title="filled"
                  >
                    filled
                  </div>
                </label>
                <label
                  class="ant-segmented-item"
                >
                  <input
                    class="ant-segmented-item-input"
                    name="test-id"
                    type="radio"
                  />
                  <div
                    aria-selected="false"
                    class="ant-segmented-item-label"
                    title="borderless"
                  >
                    borderless
                  </div>
                </label>
                <label
                  class="ant-segmented-item"
                >
                  <input
                    class="ant-segmented-item-input"
                    name="test-id"
                    type="radio"
                  />
                  <div
                    aria-selected="false"
                    class="ant-segmented-item-label"
                    title="underlined"
                  >
                    underlined
                  </div>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class="ant-form-item-required"
          for="Input"
          title="Input"
        >
          Input
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              aria-required="true"
              class="ant-input ant-input-filled"
              id="Input"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class="ant-form-item-required"
          for="InputNumber"
          title="InputNumber"
        >
          InputNumber
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-input-number ant-input-number-in-form-item ant-input-number-filled"
              style="width:100%"
            >
              <div
                class="ant-input-number-handler-wrap"
              >
                <span
                  aria-disabled="false"
                  aria-label="Increase Value"
                  class="ant-input-number-handler ant-input-number-handler-up"
                  role="button"
                  unselectable="on"
                >
                  <span
                    aria-label="up"
                    class="anticon anticon-up ant-input-number-handler-up-inner"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="up"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                      />
                    </svg>
                  </span>
                </span>
                <span
                  aria-disabled="false"
                  aria-label="Decrease Value"
                  class="ant-input-number-handler ant-input-number-handler-down"
                  role="button"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-input-number-handler-down-inner"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-input-number-input-wrap"
              >
                <input
                  aria-required="true"
                  autocomplete="off"
                  class="ant-input-number-input"
                  id="InputNumber"
                  role="spinbutton"
                  step="1"
                  value=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class="ant-form-item-required"
          for="TextArea"
          title="TextArea"
        >
          TextArea
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <textarea
              aria-required="true"
              class="ant-input ant-input-filled"
              id="TextArea"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class="ant-form-item-required"
          for="Mentions"
          title="Mentions"
        >
          Mentions
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-mentions ant-mentions-filled"
            >
              <textarea
                aria-required="true"
                class="rc-textarea"
                id="Mentions"
                rows="1"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class="ant-form-item-required"
          for="Select"
          title="Select"
        >
          Select
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              aria-required="true"
              class="ant-select ant-select-filled ant-select-in-form-item ant-select-single ant-select-show-arrow"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="Select_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="Select_list"
                      aria-required="true"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      id="Select"
                      readonly=""
                      role="combobox"
                      style="opacity:0"
                      type="search"
                      unselectable="on"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  />
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class="ant-form-item-required"
          for="Cascader"
          title="Cascader"
        >
          Cascader
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              aria-required="true"
              class="ant-select ant-cascader ant-select-filled ant-select-in-form-item ant-select-single ant-select-allow-clear ant-select-show-arrow"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="Cascader_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="Cascader_list"
                      aria-required="true"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      id="Cascader"
                      readonly=""
                      role="combobox"
                      style="opacity:0"
                      type="search"
                      unselectable="on"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  />
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class="ant-form-item-required"
          for="TreeSelect"
          title="TreeSelect"
        >
          TreeSelect
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              aria-required="true"
              class="ant-select ant-tree-select ant-select-filled ant-select-in-form-item ant-select-single ant-select-show-arrow"
            >
              <div
                class="ant-select-selector"
              >
                <span
                  class="ant-select-selection-wrap"
                >
                  <span
                    class="ant-select-selection-search"
                  >
                    <input
                      aria-autocomplete="list"
                      aria-controls="TreeSelect_list"
                      aria-expanded="false"
                      aria-haspopup="listbox"
                      aria-owns="TreeSelect_list"
                      aria-required="true"
                      autocomplete="off"
                      class="ant-select-selection-search-input"
                      id="TreeSelect"
                      readonly=""
                      role="combobox"
                      style="opacity:0"
                      type="search"
                      unselectable="on"
                      value=""
                    />
                  </span>
                  <span
                    class="ant-select-selection-placeholder"
                  />
                </span>
              </div>
              <span
                aria-hidden="true"
                class="ant-select-arrow"
                style="user-select:none;-webkit-user-select:none"
                unselectable="on"
              >
                <span
                  aria-label="down"
                  class="anticon anticon-down ant-select-suffix"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="down"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class="ant-form-item-required"
          for="DatePicker"
          title="DatePicker"
        >
          DatePicker
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-picker ant-picker-filled"
            >
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  aria-required="true"
                  autocomplete="off"
                  id="DatePicker"
                  placeholder="Select date"
                  size="12"
                  value=""
                />
                <span
                  class="ant-picker-suffix"
                >
                  <span
                    aria-label="calendar"
                    class="anticon anticon-calendar"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="calendar"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label ant-col-xs-24 ant-col-sm-6"
      >
        <label
          class="ant-form-item-required"
          for="RangePicker"
          title="RangePicker"
        >
          RangePicker
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control ant-col-xs-24 ant-col-sm-14"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-picker ant-picker-range ant-picker-filled"
            >
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  aria-required="true"
                  autocomplete="off"
                  date-range="start"
                  id="RangePicker"
                  placeholder="Start date"
                  size="12"
                  value=""
                />
              </div>
              <div
                class="ant-picker-range-separator"
              >
                <span
                  aria-label="to"
                  class="ant-picker-separator"
                >
                  <span
                    aria-label="swap-right"
                    class="anticon anticon-swap-right"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="swap-right"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="0 0 1024 1024"
                      width="1em"
                    >
                      <path
                        d="M873.1 596.2l-164-208A32 32 0 00684 376h-64.8c-6.7 0-10.4 7.7-6.3 13l144.3 183H152c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h695.9c26.8 0 41.7-30.8 25.2-51.8z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-picker-input"
              >
                <input
                  aria-invalid="false"
                  aria-required="true"
                  autocomplete="off"
                  date-range="end"
                  placeholder="End date"
                  size="12"
                  value=""
                />
              </div>
              <div
                class="ant-picker-active-bar"
                style="position:absolute;width:0"
              />
              <span
                class="ant-picker-suffix"
              >
                <span
                  aria-label="calendar"
                  class="anticon anticon-calendar"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    data-icon="calendar"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z"
                    />
                  </svg>
                </span>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-16 ant-col-offset-6 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <button
              class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
              type="submit"
            >
              <span>
                Submit
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/warning-only.tsx correctly 1`] = `
<form
  autocomplete="off"
  class="ant-form ant-form-vertical"
>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-label"
      >
        <label
          class="ant-form-item-required"
          for="url"
          title="URL"
        >
          URL
        </label>
      </div>
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <input
              aria-required="true"
              class="ant-input ant-input-outlined"
              id="url"
              placeholder="input placeholder"
              type="text"
              value=""
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    class="ant-form-item"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-space ant-space-horizontal ant-space-align-center ant-space-gap-row-small ant-space-gap-col-small"
            >
              <div
                class="ant-space-item"
              >
                <button
                  class="ant-btn ant-btn-primary ant-btn-color-primary ant-btn-variant-solid"
                  type="submit"
                >
                  <span>
                    Submit
                  </span>
                </button>
              </div>
              <div
                class="ant-space-item"
              >
                <button
                  class="ant-btn ant-btn-default ant-btn-color-default ant-btn-variant-outlined"
                  type="button"
                >
                  <span>
                    Fill
                  </span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;

exports[`renders components/form/demo/without-form-create.tsx correctly 1`] = `
<form
  class="ant-form ant-form-horizontal"
  style="max-width:600px"
>
  <div
    class="ant-form-item ant-form-item-with-help"
  >
    <div
      class="ant-row ant-form-item-row"
    >
      <div
        class="ant-col ant-col-7 ant-form-item-label"
      >
        <label
          class=""
          title="Prime between 8 & 12"
        >
          Prime between 8 & 12
        </label>
      </div>
      <div
        class="ant-col ant-col-12 ant-form-item-control"
      >
        <div
          class="ant-form-item-control-input"
        >
          <div
            class="ant-form-item-control-input-content"
          >
            <div
              class="ant-input-number ant-input-number-in-form-item ant-input-number-outlined"
            >
              <div
                class="ant-input-number-handler-wrap"
              >
                <span
                  aria-disabled="false"
                  aria-label="Increase Value"
                  class="ant-input-number-handler ant-input-number-handler-up"
                  role="button"
                  unselectable="on"
                >
                  <span
                    aria-label="up"
                    class="anticon anticon-up ant-input-number-handler-up-inner"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="up"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M890.5 755.3L537.9 269.2c-12.8-17.6-39-17.6-51.7 0L133.5 755.3A8 8 0 00140 768h75c5.1 0 9.9-2.5 12.9-6.6L512 369.8l284.1 391.6c3 4.1 7.8 6.6 12.9 6.6h75c6.5 0 10.3-7.4 6.5-12.7z"
                      />
                    </svg>
                  </span>
                </span>
                <span
                  aria-disabled="false"
                  aria-label="Decrease Value"
                  class="ant-input-number-handler ant-input-number-handler-down"
                  role="button"
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    class="anticon anticon-down ant-input-number-handler-down-inner"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
              <div
                class="ant-input-number-input-wrap"
              >
                <input
                  aria-valuemax="12"
                  aria-valuemin="8"
                  aria-valuenow="11"
                  autocomplete="off"
                  class="ant-input-number-input"
                  role="spinbutton"
                  step="1"
                  value="11"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</form>
`;
