/**
 * Convert a value or array to an array
 * @param candidate - The value to convert to array
 * @param skipEmpty - Whether to return empty array for null/undefined values
 * @returns Array containing the value(s)
 */
const toList = <T>(candidate: T | T[], skipEmpty = false): T[] => {
  // Handle null/undefined values when skipEmpty is true
  if (skipEmpty && (candidate === undefined || candidate === null)) {
    return [];
  }

  // Return as-is if already an array, otherwise wrap in array
  return Array.isArray(candidate) ? candidate : [candidate];
};

export default toList;
