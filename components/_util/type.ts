import type React from 'react';

/**
 * Literal union type that allows both specific string literals and arbitrary strings
 * @see https://github.com/Microsoft/TypeScript/issues/29729
 */
export type LiteralUnion<T extends string> = T | (string & {});

/**
 * Generic object type with any property keys and values
 * @deprecated Consider using more specific types when possible for better type safety
 */
export type AnyObject = Record<PropertyKey, any>;

/**
 * Represents a component that can be either a React component or a string (for HTML elements)
 */
export type CustomComponent<P = Record<string, any>> = React.ComponentType<P> | string;

/**
 * Extract props type from a React component or object
 * @example
 * ```ts
 * import { Checkbox } from 'antd'
 * import type { GetProps } from 'antd';
 *
 * type CheckboxGroupProps = GetProps<typeof Checkbox.Group>
 * ```
 * @since 5.13.0
 */
export type GetProps<T extends React.ComponentType<any> | object> = T extends React.ComponentType<
  infer P
>
  ? P
  : T extends object
    ? T
    : never;

/**
 * Extract a specific prop type from a React component or object
 * @example
 * ```ts
 * import { Select } from 'antd';
 * import type { GetProp, SelectProps } from 'antd';
 *
 * type SelectOption1 = GetProp<SelectProps, 'options'>[number];
 * // or
 * type SelectOption2 = GetProp<typeof Select, 'options'>[number];
 *
 * const onChange: GetProp<typeof Select, 'onChange'> = (value, option) => {
 *  // Do something
 * };
 * ```
 * @since 5.13.0
 */
export type GetProp<
  T extends React.ComponentType<any> | object,
  PropName extends keyof GetProps<T>,
> = NonNullable<GetProps<T>[PropName]>;

/**
 * Component type that accepts a ref prop
 */
type ReactRefComponent<Props extends { ref?: React.Ref<any> | string }> = (
  props: Props,
) => React.ReactNode;

/**
 * Extract ref type from React.RefAttributes
 */
type ExtractRefAttributesRef<T> = T extends React.RefAttributes<infer P> ? P : never;

/**
 * Extract ref type from a React component
 * @example
 * ```ts
 * import { Input } from 'antd';
 * import type { GetRef } from 'antd';
 *
 * type InputRef = GetRef<typeof Input>;
 * ```
 * @since 5.13.0
 */
export type GetRef<T extends ReactRefComponent<any> | React.Component<any>> =
  T extends React.Component<any>
    ? T
    : T extends React.ComponentType<infer P>
      ? ExtractRefAttributesRef<P>
      : never;

/**
 * Extract props type from React Context
 */
export type GetContextProps<T> = T extends React.Context<infer P> ? P : never;

/**
 * Extract a specific prop type from React Context
 */
export type GetContextProp<
  T extends React.Context<any>,
  PropName extends keyof GetContextProps<T>,
> = NonNullable<GetContextProps<T>[PropName]>;
