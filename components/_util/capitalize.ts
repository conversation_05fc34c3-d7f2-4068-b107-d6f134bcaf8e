/**
 * Capitalize the first letter of a string
 * @param str - The string to capitalize
 * @returns The capitalized string
 */
export default function capitalize<T extends string>(str: T): Capitalize<T> {
  // Type guard: ensure input is a string
  if (typeof str !== 'string') {
    return str;
  }

  // Handle empty string
  if (str.length === 0) {
    return str as Capitalize<T>;
  }

  // Capitalize first character and concatenate with rest
  const ret = str.charAt(0).toUpperCase() + str.slice(1);
  return ret as Capitalize<T>;
}
