import React from 'react';

import type { GlobalToken } from '../theme/internal';
import { useToken } from '../theme/internal';
import { addMediaQueryListener, removeMediaQueryListener } from './mediaQueryUtil';

/** Supported breakpoint names */
export type Breakpoint = 'xxl' | 'xl' | 'lg' | 'md' | 'sm' | 'xs';

/** Map of breakpoint names to media query strings */
export type BreakpointMap = Record<Breakpoint, string>;

/** Map of breakpoint names to boolean values indicating if they match */
export type ScreenMap = Partial<Record<Breakpoint, boolean>>;

/** Map of breakpoint names to numeric sizes */
export type ScreenSizeMap = Partial<Record<Breakpoint, number>>;

/** Array of breakpoints in descending order of size */
export const responsiveArray: Breakpoint[] = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];

/** Callback function type for screen change subscriptions */
type SubscribeFunc = (screens: ScreenMap) => void;

/**
 * Generate responsive breakpoint map from theme tokens
 * @param token - Global theme token containing screen size values
 * @returns Map of breakpoint names to media query strings
 */
const getResponsiveMap = (token: GlobalToken): BreakpointMap => ({
  xs: `(max-width: ${token.screenXSMax}px)`,
  sm: `(min-width: ${token.screenSM}px)`,
  md: `(min-width: ${token.screenMD}px)`,
  lg: `(min-width: ${token.screenLG}px)`,
  xl: `(min-width: ${token.screenXL}px)`,
  xxl: `(min-width: ${token.screenXXL}px)`,
});

/**
 * Validates that breakpoint tokens are in correct order
 * For each breakpoint: screenMin <= screen <= screenMax and screenMax <= nextScreenMin
 * @param token - Global theme token to validate
 * @returns The validated token
 * @throws Error if breakpoint values are invalid
 */
const validateBreakpoints = (token: GlobalToken): GlobalToken => {
  const indexableToken: Record<string, number> = token as any;
  const revBreakpoints = [...responsiveArray].reverse();

  revBreakpoints.forEach((breakpoint, i) => {
    const breakpointUpper = breakpoint.toUpperCase();
    const screenMin = `screen${breakpointUpper}Min`;
    const screen = `screen${breakpointUpper}`;

    if (!(indexableToken[screenMin] <= indexableToken[screen])) {
      throw new Error(
        `${screenMin}<=${screen} fails : !(${indexableToken[screenMin]}<=${indexableToken[screen]})`,
      );
    }

    if (i < revBreakpoints.length - 1) {
      const screenMax = `screen${breakpointUpper}Max`;

      if (!(indexableToken[screen] <= indexableToken[screenMax])) {
        throw new Error(
          `${screen}<=${screenMax} fails : !(${indexableToken[screen]}<=${indexableToken[screenMax]})`,
        );
      }

      const nextBreakpointUpperMin = revBreakpoints[i + 1].toUpperCase();
      const nextScreenMin = `screen${nextBreakpointUpperMin}Min`;

      if (!(indexableToken[screenMax] <= indexableToken[nextScreenMin])) {
        throw new Error(
          `${screenMax}<=${nextScreenMin} fails : !(${indexableToken[screenMax]}<=${indexableToken[nextScreenMin]})`,
        );
      }
    }
  });
  return token;
};

/**
 * Find the first matching screen size based on current screen state
 * @param screens - Current screen breakpoint states
 * @param screenSizes - Map of breakpoint names to sizes
 * @returns The size for the first matching breakpoint, or undefined
 */
export const matchScreen = (screens: ScreenMap, screenSizes?: ScreenSizeMap): number | undefined => {
  if (!screenSizes) {
    return undefined;
  }

  for (const breakpoint of responsiveArray) {
    if (screens[breakpoint] && screenSizes[breakpoint] !== undefined) {
      return screenSizes[breakpoint];
    }
  }

  return undefined;
};

/**
 * Interface for responsive observer instance
 */
interface ResponsiveObserverType {
  /** Map of breakpoint names to media query strings */
  responsiveMap: BreakpointMap;
  /** Dispatch screen changes to all subscribers */
  dispatch: (map: ScreenMap) => boolean;
  /** Subscribe to screen changes */
  subscribe: (func: SubscribeFunc) => number;
  /** Unsubscribe from screen changes */
  unsubscribe: (token: number) => void;
  /** Register media query listeners */
  register: () => void;
  /** Unregister media query listeners */
  unregister: () => void;
  /** Map of media queries to their handlers */
  matchHandlers: Record<
    PropertyKey,
    {
      mql: MediaQueryList;
      listener: (this: MediaQueryList, ev: MediaQueryListEvent) => void;
    }
  >;
}

/**
 * Hook that provides responsive breakpoint observation functionality
 * @returns ResponsiveObserver instance with subscription methods
 */
const useResponsiveObserver = (): ResponsiveObserverType => {
  const [, token] = useToken();
  const responsiveMap = getResponsiveMap(validateBreakpoints(token));

  // Memoize the observer instance to avoid recreation on every render
  return React.useMemo<ResponsiveObserverType>(() => {
    const subscribers = new Map<number, SubscribeFunc>();
    let subUid = -1;
    let screens: Partial<Record<Breakpoint, boolean>> = {};
    return {
      responsiveMap,
      matchHandlers: {},
      dispatch(pointMap: ScreenMap) {
        screens = pointMap;
        subscribers.forEach((func) => func(screens));
        return subscribers.size >= 1;
      },
      subscribe(func: SubscribeFunc): number {
        if (!subscribers.size) {
          this.register();
        }
        subUid += 1;
        subscribers.set(subUid, func);
        func(screens);
        return subUid;
      },
      unsubscribe(paramToken: number) {
        subscribers.delete(paramToken);
        if (!subscribers.size) {
          this.unregister();
        }
      },
      register() {
        Object.entries(responsiveMap).forEach(([screen, mediaQuery]) => {
          const listener = ({ matches }: { matches: boolean }) => {
            this.dispatch({ ...screens, [screen]: matches });
          };
          const mql = window.matchMedia(mediaQuery);
          addMediaQueryListener(mql, listener);
          this.matchHandlers[mediaQuery] = { mql, listener };
          listener(mql);
        });
      },
      unregister() {
        Object.values(responsiveMap).forEach((mediaQuery) => {
          const handler = this.matchHandlers[mediaQuery];
          removeMediaQueryListener(handler?.mql, handler?.listener);
        });
        subscribers.clear();
      },
    };
  }, [token]);
};

export default useResponsiveObserver;
