/**
 * Check if a value is a primitive type (string, number, boolean, null, undefined, symbol, bigint)
 * @param value - The value to check
 * @returns true if the value is primitive, false otherwise
 */
const isPrimitive = (value: unknown): value is string | number | boolean | null | undefined | symbol | bigint => {
  // null is a special case - typeof null === 'object' but it's primitive
  if (value === null) {
    return true;
  }

  // Check for non-object and non-function types (primitives)
  const type = typeof value;
  return type !== 'object' && type !== 'function';
};

export default isPrimitive;
