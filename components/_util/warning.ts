import * as React from 'react';
import rcWarning, { resetWarned as rcR<PERSON>tWarned } from 'rc-util/lib/warning';

/**
 * No-operation function for production builds
 */
export function noop(): void {}

/**
 * Cache for deprecated warnings to avoid duplicate messages
 */
let deprecatedWarnList: Record<string, string[]> | null = null;

/**
 * Reset all warning states - useful for testing
 */
export function resetWarned(): void {
  deprecatedWarnList = null;
  rcResetWarned();
}

/**
 * Warning function signature
 */
type Warning = (valid: boolean, component: string, message?: string) => void;

/**
 * Internal warning function - noop in production, active in development
 */
let _warning: Warning = noop;

if (process.env.NODE_ENV !== 'production') {
  _warning = (valid, component, message) => {
    rcWarning(valid, `[antd: ${component}] ${message}`);

    // Reset warnings in test environment to avoid interference between tests
    if (process.env.NODE_ENV === 'test') {
      resetWarned();
    }
  };
}
/**
 * Main warning function - exported for external use
 */
const warning = _warning;

/**
 * Base type warning function signature
 */
type BaseTypeWarning = (
  valid: boolean,
  /**
   * Warning types:
   * - deprecated: Some API will be removed in future but still support now.
   * - usage: Some API usage is not correct.
   * - breaking: Breaking change like API is removed.
   */
  type: 'deprecated' | 'usage' | 'breaking',
  message?: string,
) => void;

/**
 * Extended type warning with deprecated helper method
 */
type TypeWarning = BaseTypeWarning & {
  deprecated: (valid: boolean, oldProp: string, newProp: string, message?: string) => void;
};

export interface WarningContextProps {
  /**
   * @descCN 设置警告等级，设置 `false` 时会将废弃相关信息聚合为单条信息。
   * @descEN Set the warning level. When set to `false`, discard related information will be aggregated into a single message.
   * @since 5.10.0
   */
  strict?: boolean;
}

export const WarningContext = React.createContext<WarningContextProps>({});

/**
 * Development-only warning hook
 * Note: This is a hook but not named as `useWarning` since it's only used in development.
 * Should always be wrapped in `if (process.env.NODE_ENV !== 'production')` condition
 * @param component - Component name for warning context
 * @returns TypeWarning function with deprecated helper
 */
export const devUseWarning: (component: string) => TypeWarning =
  process.env.NODE_ENV !== 'production'
    ? (component) => {
        const { strict } = React.useContext(WarningContext);

        const typeWarning: TypeWarning = (valid, type, message) => {
          if (!valid) {
            if (strict === false && type === 'deprecated') {
              // Handle deprecated warnings with aggregation in non-strict mode
              const existWarning = deprecatedWarnList;

              if (!deprecatedWarnList) {
                deprecatedWarnList = {};
              }

              deprecatedWarnList[component] = deprecatedWarnList[component] || [];
              const warningMessage = message || '';

              if (!deprecatedWarnList[component].includes(warningMessage)) {
                deprecatedWarnList[component].push(warningMessage);
              }

              // Show aggregated warning only on first occurrence
              if (!existWarning) {
                console.warn(
                  '[antd] There exists deprecated usage in your code:',
                  deprecatedWarnList,
                );
              }
            } else {
              // Use standard warning for non-deprecated or strict mode
              warning(valid, component, message);
            }
          }
        };

        // Add deprecated helper method
        typeWarning.deprecated = (valid, oldProp, newProp, message) => {
          typeWarning(
            valid,
            'deprecated',
            `\`${oldProp}\` is deprecated. Please use \`${newProp}\` instead.${
              message ? ` ${message}` : ''
            }`,
          );
        };

        return typeWarning;
      }
    : () => {
        // Production no-op implementation
        const noopWarning: TypeWarning = () => {};
        noopWarning.deprecated = noop;
        return noopWarning;
      };

export default warning;
