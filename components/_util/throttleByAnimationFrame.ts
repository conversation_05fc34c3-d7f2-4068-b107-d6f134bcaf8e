import raf from 'rc-util/lib/raf';

/**
 * Throttle function that uses requestAnimationFrame to limit execution frequency
 * @param fn - The function to throttle
 * @returns Throttled function with cancel method
 */
function throttleByAnimationFrame<T extends any[]>(fn: (...args: T) => void) {
  let requestId: number | null = null;

  const later = (args: T) => () => {
    requestId = null;
    fn(...args);
  };

  const throttled = (...args: T) => {
    // Only schedule if not already scheduled
    if (requestId === null) {
      requestId = raf(later(args));
    }
  };

  throttled.cancel = () => {
    if (requestId !== null) {
      raf.cancel(requestId);
      requestId = null;
    }
  };

  return throttled;
}

export default throttleByAnimationFrame;
