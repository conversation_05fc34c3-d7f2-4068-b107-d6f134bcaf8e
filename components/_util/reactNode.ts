import React from 'react';

/**
 * Props type for render functions - can be an object or a function that transforms props
 */
type RenderProps = Record<string, any> | ((originProps: Record<string, any>) => Record<string, any> | undefined);

/**
 * Check if a React node is a Fragment
 * @param child - The React node to check
 * @returns true if the node is a Fragment, false otherwise
 */
export function isFragment(child: any): child is React.ReactElement {
  return child && React.isValidElement(child) && child.type === React.Fragment;
}

/**
 * Replace a React element with another element, optionally merging props
 * @param element - The original element to replace
 * @param replacement - The replacement element
 * @param props - Props to merge (object or function)
 * @returns The replacement element or the modified original element
 */
export const replaceElement = <P = any>(
  element: React.ReactNode,
  replacement: React.ReactNode,
  props?: RenderProps,
): React.ReactNode => {
  // If element is not valid, return replacement as-is
  if (!React.isValidElement<P>(element)) {
    return replacement;
  }

  // Clone element with merged props
  const mergedProps = typeof props === 'function'
    ? props(element.props || {})
    : props;

  return React.cloneElement<P>(element, mergedProps);
};

/**
 * Clone a React element with optional props
 * @param element - The element to clone
 * @param props - Props to merge (object or function)
 * @returns The cloned element
 */
export function cloneElement<P = any>(element: React.ReactNode, props?: RenderProps): React.ReactElement<P> | null {
  const result = replaceElement<P>(element, element, props);
  return React.isValidElement<P>(result) ? result : null;
}
