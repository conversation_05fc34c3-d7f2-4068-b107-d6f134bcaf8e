import * as React from 'react';

import useForceUpdate from './useForceUpdate';

/**
 * Return type for useSyncState hook
 */
type UseSyncStateProps<T> = readonly [() => T, (newValue: T) => void];

/**
 * Hook that provides synchronous state management with immediate updates
 * Unlike useState, the getter always returns the current value immediately
 * @param initialValue - The initial state value
 * @returns A tuple with getter and setter functions
 */
export default function useSyncState<T>(initialValue: T): UseSyncStateProps<T> {
  const ref = React.useRef<T>(initialValue);
  const forceUpdate = useForceUpdate();

  const getter = React.useCallback(() => ref.current, []);

  const setter = React.useCallback((newValue: T) => {
    // Only update if value actually changed
    if (ref.current !== newValue) {
      ref.current = newValue;
      // Trigger re-render
      forceUpdate();
    }
  }, [forceUpdate]);

  return [getter, setter] as const;
}
