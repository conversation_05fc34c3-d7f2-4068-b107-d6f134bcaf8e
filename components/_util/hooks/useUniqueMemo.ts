import React from 'react';

/** Cache cleanup interval - 10 minutes */
const BEAT_LIMIT = 1000 * 60 * 10;

/** Access count threshold for triggering cleanup */
const CLEANUP_THRESHOLD = 10000;

/**
 * A helper class to map dependency arrays to cached values.
 * It supports both primitive keys and object keys with automatic cleanup.
 */
class ArrayKeyMap<T> {
  private map = new Map<string, T>();

  // Use WeakMap to avoid memory leak for object references
  private objectIDMap = new WeakMap<object, number>();

  private nextID = 0;
  private lastAccessBeat = new Map<string, number>();

  // Track access count for cleanup optimization
  private accessBeat = 0;

  /**
   * Set a value for the given dependency keys
   */
  set(keys: React.DependencyList, value: T): void {
    // Trigger cleanup if needed
    this.clear();

    const compositeKey = this.getCompositeKey(keys);
    this.map.set(compositeKey, value);
    this.lastAccessBeat.set(compositeKey, Date.now());
  }

  /**
   * Get a cached value for the given dependency keys
   */
  get(keys: React.DependencyList): T | undefined {
    const compositeKey = this.getCompositeKey(keys);

    const cache = this.map.get(compositeKey);

    // Update access time for cache management
    if (cache !== undefined) {
      this.lastAccessBeat.set(compositeKey, Date.now());
    }

    this.accessBeat += 1;
    return cache;
  }

  /**
   * Generate a composite key from dependency array
   */
  private getCompositeKey(keys: React.DependencyList): string {
    const ids = keys.map<string>((key) => {
      if (key && typeof key === 'object') {
        return `obj_${this.getObjectID(key)}`;
      }
      return `${typeof key}_${key}`;
    });
    return ids.join('|');
  }

  /**
   * Get or create a unique ID for an object
   */
  private getObjectID(obj: object): number {
    if (this.objectIDMap.has(obj)) {
      return this.objectIDMap.get(obj)!;
    }

    const id = this.nextID;
    this.objectIDMap.set(obj, id);
    this.nextID += 1;

    return id;
  }

  /**
   * Clean up expired cache entries
   */
  private clear(): void {
    if (this.accessBeat > CLEANUP_THRESHOLD) {
      const now = Date.now();

      this.lastAccessBeat.forEach((beat, key) => {
        if (now - beat > BEAT_LIMIT) {
          this.map.delete(key);
          this.lastAccessBeat.delete(key);
        }
      });

      this.accessBeat = 0;
    }
  }
}

/** Global cache instance shared across all hook instances */
const uniqueMap = new ArrayKeyMap<any>();

/**
 * Like `useMemo`, but this hook result will be shared across all component instances.
 * This can help reduce memory usage when multiple components use the same expensive computation.
 *
 * @param memoFn - Function that computes the memoized value
 * @param deps - Dependency array for memoization
 * @returns The memoized value
 */
function useUniqueMemo<T>(memoFn: () => T, deps: React.DependencyList): T {
  return React.useMemo<T>(() => {
    const cachedValue = uniqueMap.get(deps);
    if (cachedValue !== undefined) {
      return cachedValue as T;
    }

    const newValue = memoFn();
    uniqueMap.set(deps, newValue);
    return newValue;
  }, deps);
}

export default useUniqueMemo;
