import { defaultPrefixCls } from '../../config-provider';
import type { GlobalToken } from '../../theme/internal';

/** CSS class name for wave target elements */
export const TARGET_CLS = `${defaultPrefixCls}-wave-target`;

/**
 * Wave effect information passed to the effect function
 */
export interface WaveEffectInfo {
  /** CSS class name for the wave effect */
  className: string;
  /** Global theme token */
  token: GlobalToken;
  /** Component type that triggered the wave */
  component?: WaveComponent;
  /** Mouse event that triggered the wave */
  event: MouseEvent;
  /** Hash ID for CSS-in-JS */
  hashId: string;
}

/**
 * Function signature for showing wave effect
 */
export type ShowWaveEffect = (element: HTMLElement, info: WaveEffectInfo) => void;

/**
 * Function signature for wave trigger
 */
export type ShowWave = (event: MouseEvent) => void;

/**
 * Components that support wave effect
 */
export type WaveComponent = 'Tag' | 'Button' | 'Checkbox' | 'Radio' | 'Switch';
