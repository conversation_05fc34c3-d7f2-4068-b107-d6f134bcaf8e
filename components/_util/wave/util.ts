/**
 * Check if a color is valid for wave effect (not white or transparent)
 * @param color - The color string to validate
 * @returns true if the color is valid for wave effect
 */
export function isValidWaveColor(color: string): boolean {
  if (!color) {
    return false;
  }

  // Check for white colors in various formats
  const whiteColors = ['#fff', '#ffffff', 'rgb(255, 255, 255)', 'rgba(255, 255, 255, 1)'];
  if (whiteColors.includes(color.toLowerCase())) {
    return false;
  }

  // Check for transparent colors
  if (color === 'transparent') {
    return false;
  }

  // Check for transparent rgba colors (alpha = 0)
  if (/rgba\((?:\d+,\s*){3}0\)/.test(color)) {
    return false;
  }

  return true;
}

/**
 * Get the most appropriate color for wave effect from an element
 * Priority: borderTopColor > borderColor > backgroundColor
 * @param node - The HTML element to extract color from
 * @returns The color string or null if no valid color found
 */
export function getTargetWaveColor(node: HTMLElement): string | null {
  const computedStyle = getComputedStyle(node);
  const { borderTopColor, borderColor, backgroundColor } = computedStyle;

  // Check colors in priority order
  const colorsToCheck = [borderTopColor, borderColor, backgroundColor];

  for (const color of colorsToCheck) {
    if (isValidWaveColor(color)) {
      return color;
    }
  }

  return null;
}
